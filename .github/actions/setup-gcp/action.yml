name: 'Setup GCP Environment'
description: 'Sets up Google Cloud SDK and configures authentication'

inputs:
  credentials_json:
    description: 'Google Cloud credentials JSON'
    required: true

runs:
  using: "composite"
  steps:
    - id: 'auth'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: ${{ inputs.credentials_json }}

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v2'
      with:
        version: '>= 520.0.0'
