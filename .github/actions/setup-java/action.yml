name: 'Setup Java Environment'
description: 'Sets up Java and Gradle for backend services'

runs:
  using: "composite"
  steps:
    - name: Setup JDK
      uses: actions/setup-java@v4
      with:
        java-version: 21
        distribution: corretto

    - name: Setup Gradle
      uses: gradle/actions/setup-gradle@v4

    - name: Disable Gradle Daemon for CI
      shell: bash
      run: |
        echo "GRADLE_OPTS=-Dorg.gradle.daemon=false" >> $GITHUB_ENV
