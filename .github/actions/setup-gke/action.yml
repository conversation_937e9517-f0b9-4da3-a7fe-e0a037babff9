name: 'Setup GKE Environment'
description: 'Sets up kubectl for GKE'

inputs:
  credentials_json:
    description: 'Google Cloud credentials JSON'
    required: true
  gke-cluster:
    description: 'GKE cluster name'
    required: true
  gke-location:
    description: 'GKE cluster location'
    required: true

runs:
  using: "composite"
  steps:
    - name: Setup GCP Environment
      uses: ./.github/actions/setup-gcp
      with:
        credentials_json: ${{ inputs.credentials_json }}

    - name: Set up kubectl
      uses: google-github-actions/get-gke-credentials@v2
      with:
        cluster_name: ${{ inputs.gke-cluster }}
        location: ${{ inputs.gke-location }}
