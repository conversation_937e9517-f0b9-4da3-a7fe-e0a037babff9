name: 'Docker Build and Push'
description: 'Builds and pushes a Docker image to GCP Artifact Registry'

inputs:
  service:
    description: 'Service name'
    required: true
  working-directory:
    description: 'Directory containing the Dockerfile'
    required: true
  namespace:
    description: 'Kubernetes namespace to deploy to'
    required: true
  dockerfile:
    description: 'Dockerfile name'
    required: false
    default: 'Dockerfile'
  build-args:
    description: 'Additional build arguments for Docker'
    required: false
    default: ''
  credentials_json:
    description: 'Google Cloud credentials JSON, this is a secret!'
    required: true
  gcp-project-id:
    description: 'GCP Project ID'
    required: true
  gcp-artifact-registry:
    description: 'GCP Artifact Registry host'
    required: true
  gcp-artifact-registry-docker-repository:
    description: 'GCP Artifact Registry repository name'
    required: true

outputs:
  service:
    description: 'Service name used for deployment, if dev branch, it will be <service>-<branch>'
    value: ${{ steps.build_image.outputs.service }}
  image:
    description: 'Full image name with tag'
    value: ${{ steps.build_image.outputs.image }}

runs:
  using: "composite"
  steps:
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up Google Cloud SDK
      uses: ./.github/actions/setup-gcp
      with:
        credentials_json: ${{ inputs.credentials_json }}

    - name: Build and Push Docker image
      id: build_image
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        BRANCH=$(git branch --show-current)
        COMMIT=$(git rev-parse --short HEAD)
        DATETIME=$(date +%Y%m%d%H%M%S)

        IMAGE_PREFIX=${{ inputs.gcp-artifact-registry }}/${{ inputs.gcp-project-id }}/${{ inputs.gcp-artifact-registry-docker-repository }}
        
        # 如果是 main 分支
        #   如果是 production 环境：<service>-<namespace>:<datetime>-<commit>，因为需要做回滚
        #   如果是 testing 环境：<service>-<namespace>:latest，testing 的 main 暂时不需要回滚
        # 如果是 dev 分支
        #   如果是 production 环境：<service>-<branch>-<namespace>:latest，因为 dev 分支不需要回滚
        #   如果是 testing 环境：<service>-<branch>-<namespace>:latest，因为 dev 分支不需要回滚
        
        if [[ "${BRANCH}" == "main" ]]; then
            SERVICE=${{ inputs.service }}
        else
            SERVICE=${{ inputs.service }}-${BRANCH}
        fi
        
        IMAGE_NAME=${SERVICE}-${{ inputs.namespace }}
        
        if [[ "${{ inputs.namespace }}" == "production" && "${BRANCH}" == "main" ]]; then
            IMAGE_TAG=${DATETIME}-${COMMIT}
        else
            IMAGE_TAG=latest
        fi
        
        IMAGE=${IMAGE_PREFIX}/${IMAGE_NAME}:${IMAGE_TAG}
    
        # 配置 Docker 认证
        gcloud auth configure-docker ${{ inputs.gcp-artifact-registry }}

        # 构建并推送镜像
        BUILD_ARGS=""
        if [ -n "${{ inputs.build-args }}" ]; then
          BUILD_ARGS="${{ inputs.build-args }}"
        fi

        echo "Building Docker image: ${IMAGE}"
        echo "Build arguments: ${BUILD_ARGS}"
        
        docker buildx build --push -f ${{ inputs.dockerfile }} -t ${IMAGE} ${BUILD_ARGS} .

        echo "service=${SERVICE}" >> $GITHUB_OUTPUT
        echo "image=${IMAGE}" >> $GITHUB_OUTPUT
