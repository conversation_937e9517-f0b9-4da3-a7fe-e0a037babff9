name: Kubernetes Deploy

on:
  workflow_call:
    inputs:
      service:
        required: true
        type: string
        description: 'Name of the service to deploy'
      image:
        required: true
        type: string
        description: 'Name of the Docker image'
      k8s-path:
        required: true
        type: string
        description: 'Path to the Kubernetes manifests'
      namespace:
        required: true
        type: string
        description: 'Namespace to deploy to'

jobs:
  deploy:
    name: Deploy to GKE
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    defaults:
      run:
        working-directory: ${{ inputs.k8s-path }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup GCP Environment
        uses: ./.github/actions/setup-gcp
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up kubectl
        uses: google-github-actions/get-gke-credentials@v2
        with:
          cluster_name: ${{ vars.GKE_CLUSTER }}
          location: ${{ vars.GKE_LOCATION }}

      - name: Deploy
        run: |
          BRANCH=$(git branch --show-current)
          COMMIT=$(git rev-parse --short HEAD)
          DATETIME=$(date +%Y%m%d%H%M%S)

          echo "===== Runtime Variables ====="
          echo "SERVICE: ${{ inputs.service }}"
          echo "IMAGE: ${{ inputs.image }}"
          echo "BRANCH: $BRANCH"
          echo "COMMIT: $COMMIT"
          echo "DATETIME: $DATETIME"
          echo "============================="

          FINAL_MANIFEST=$(kubectl kustomize . | sed \
            -e "s|\${SERVICE}|${{ inputs.service }}|g" \
            -e "s|\${IMAGE}|${{ inputs.image }}|g" \
            -e "s|\${NAMESPACE}|${{ inputs.namespace }}|g" \
            -e "s|\${BRANCH}|\"${BRANCH}\"|g" \
            -e "s|\${COMMIT}|\"${COMMIT}\"|g" \
            -e "s|\${DATETIME}|\"${DATETIME}\"|g")

          echo "===== FINAL KUBECTL APPLY INPUT ====="
          echo "$FINAL_MANIFEST"
          echo "===================================="

          echo "$FINAL_MANIFEST" | kubectl apply -f -

          kubectl rollout status deployment/${{ inputs.service }} -n ${{ inputs.namespace }} --timeout=600s
