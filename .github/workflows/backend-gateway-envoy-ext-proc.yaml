name: Backend gateway/envoy-ext-proc CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/gateway/envoy-ext-proc/**'
      - '.github/workflows/backend-gateway-envoy-ext-proc.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# 环境变量
env:
  SERVICE: envoy-ext-proc

defaults:
  run:
    working-directory: backend/gateway/envoy-ext-proc

jobs:
  install:
    name: Install Dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go

      - name: Install Dependencies
        run: |
          go mod download
          
  lint:
    name: Lint
    needs: [ install ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go
      - uses: ./.github/actions/setup-golangci-lint
        with:
          working-directory: backend/gateway/envoy-ext-proc

  test:
    name: Test
    needs: [ install ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go

      - name: Test
        run: |
          go test -v ./...

  build-testing:
    name: Build for Testing
    runs-on: ubuntu-latest
    needs: [ install ]
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4

      - name: Build and Push Docker image for Testing
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: testing
          working-directory: .
          dockerfile: backend/gateway/envoy-ext-proc/Dockerfile
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-testing:
    name: Deploy to Testing
    needs: [ build-testing ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/gateway/envoy-ext-proc/k8s/overlays/testing
      service: envoy-ext-proc
      image: ${{ needs.build-testing.outputs.image }}
      namespace: testing
    secrets: inherit

  build-production:
    name: Build for Production
    runs-on: ubuntu-latest
    needs: [ install ]
    permissions:
      contents: read
      id-token: write
    if: github.ref == 'refs/heads/main'
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4

      - name: Build and Push Docker image for Production
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: production
          working-directory: .
          dockerfile: backend/gateway/envoy-ext-proc/Dockerfile
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-production:
    name: Deploy to Production
    needs: [ build-production ]
    permissions:
      contents: read
      id-token: write
    if: github.ref == 'refs/heads/main'
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/gateway/envoy-ext-proc/k8s/overlays/production
      service: envoy-ext-proc
      image: ${{ needs.build-production.outputs.image }}
      namespace: production
    secrets: inherit
