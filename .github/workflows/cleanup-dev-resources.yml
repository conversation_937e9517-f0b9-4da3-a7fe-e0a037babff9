name: Cleanup Resources

on:
  delete:
    branches:
      - 'dev-*'

jobs:
  cleanup:
    name: Cleanup Kubernetes Resources
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Cancel in-progress actions
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            // For delete events, get branch name from the ref
            let branch;
            if (context.eventName === 'delete') {
              // Extract branch name from refs/heads/branch-name
              branch = context.payload.ref;
              console.log(`Delete event detected for branch: ${branch}`);
            } else if (context.payload.pull_request && context.payload.pull_request.head) {
              branch = context.payload.pull_request.head.ref;
              console.log(`Pull request event detected for branch: ${branch}`);
            } else {
              console.log('Unable to determine branch name from context');
              console.log('Event name:', context.eventName);
              console.log('Payload keys:', Object.keys(context.payload));
              return; // Exit early if we can't determine the branch
            }

            if (!branch) {
              console.log('Branch name is empty, skipping workflow cancellation');
              return;
            }

            try {
              const runs = await github.rest.actions.listWorkflowRunsForRepo({
                owner: context.repo.owner,
                repo: context.repo.repo,
                branch,
                status: 'in_progress'
              });

              console.log(`Found ${runs.data.workflow_runs.length} in-progress workflow runs for branch: ${branch}`);

              for (const run of runs.data.workflow_runs) {
                console.log(`Cancelling workflow run: ${run.id} (${run.name})`);
                await github.rest.actions.cancelWorkflowRun({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  run_id: run.id
                });
              }
            } catch (error) {
              console.error('Error cancelling workflow runs:', error.message);
              // Don't fail the entire workflow if cancellation fails
            }

      - uses: ./.github/actions/setup-gke
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gke-cluster: ${{ vars.GKE_CLUSTER }}
          gke-location: ${{ vars.GKE_LOCATION }}

      - name: Cleanup Kubernetes resources
        run: |
          BRANCH="${{ github.event.ref }}"
          echo "Cleaning up resources for branch: ${BRANCH}"

          # 只处理 testing 和 production 命名空间中的资源
          for NAMESPACE in testing production; do
            echo "Cleaning up resources in ${NAMESPACE} namespace..."

            # 删除 services
            SERVICES=$(kubectl get services -n ${NAMESPACE} -o name | grep -E ".*-${BRANCH}$" || true)
            if [ -n "$SERVICES" ]; then
              echo "Deleting services in ${NAMESPACE}:"
              echo "${SERVICES}" | xargs -r kubectl delete -n ${NAMESPACE}
            fi

            # 删除 deployments
            DEPLOYMENTS=$(kubectl get deployments -n ${NAMESPACE} -o name | grep -E ".*-${BRANCH}$" || true)
            if [ -n "$DEPLOYMENTS" ]; then
              echo "Deleting deployments in ${NAMESPACE}:"
              echo "${DEPLOYMENTS}" | xargs -r kubectl delete -n ${NAMESPACE}
            fi
          done
