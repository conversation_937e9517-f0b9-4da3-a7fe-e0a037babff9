name: Backend service-app-api CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/service-app/service-app-api/**'
      - '.github/workflows/backend-service-app-api.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-buf
      - name: Lint
        run: |
          buf lint backend/service-app/service-app-api

  breaking-change-detection:
    name: Breaking Change Detection
    if: github.ref != 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-buf
      - uses: actions/checkout@v4
        with:
          path: main
          ref: main
      - name: Breaking Change Detection
        run: |
          subdir=backend/service-app/service-app-api
          buf breaking ${subdir} --against "main/.git#branch=main,subdir=${subdir}"
