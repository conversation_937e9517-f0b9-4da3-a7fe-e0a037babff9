name: Backend lib-java CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/lib-java/**'
      - '.github/workflows/backend-lib-java.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    working-directory: backend

jobs:
  compile:
    name: Compile
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Compile
        run: |
          gradle compileJava

  lint:
    name: Lint
    needs: [ compile ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Spotless, SpotBugs & Checkstyle
        run: |
          gradle spotlessCheck spotbugsMain checkstyleMain checkStyleTest

  test:
    name: Test
    needs: [ compile ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Run tests
        run: |
          gradle test
