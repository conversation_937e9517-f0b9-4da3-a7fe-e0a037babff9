name: Backend istio-controller CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/istio-controller/**'
      - '.github/workflows/backend-istio-controller.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# 环境变量
env:
  SERVICE: istio-controller

defaults:
  run:
    working-directory: backend/istio-controller

jobs:
  install:
    name: Install Dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go

      - name: Install Dependencies
        run: |
          go mod download
          
  lint:
    name: Lint
    needs: [ install ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go
      - uses: ./.github/actions/setup-golangci-lint
        with:
          working-directory: backend/istio-controller

  test:
    name: Test
    needs: [ install ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-go

      - name: Test
        run: |
          go test -v ./...

  build-testing:
    name: Build for Testing
    needs: [ install ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/docker-build-push
        id: build_image
        with:
          service: ${{ env.SERVICE }}
          working-directory: .
          dockerfile: backend/istio-controller/Dockerfile
          namespace: testing
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  build-production:
    name: Build for Production
    needs: [ install ]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/docker-build-push
        id: build_image
        with:
          service: ${{ env.SERVICE }}
          working-directory: .
          dockerfile: backend/istio-controller/Dockerfile
          namespace: production
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-testing:
    name: Deploy to Testing
    needs: [ build-testing ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/istio-controller/k8s/overlays/testing
      service: istio-controller
      image: ${{ needs.build-testing.outputs.image }}
      namespace: testing
    secrets: inherit

  deploy-production:
    name: Deploy to Production
    needs: [ build-production ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/istio-controller/k8s/overlays/production
      service: istio-controller
      image: ${{ needs.build-production.outputs.image }}
      namespace: production
    secrets: inherit
