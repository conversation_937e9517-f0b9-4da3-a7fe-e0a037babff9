name: Backend gateway/gateway CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/gateway/gateway/**'
      - '.github/workflows/backend-gateway-gateway.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# 环境变量
env:
  SERVICE: gateway

defaults:
  run:
    working-directory: backend/gateway/gateway

jobs:
  build-testing:
    name: Build for Testing
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4

      - name: Build and Push Docker image for Testing
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: testing
          working-directory: backend/gateway/gateway
          dockerfile: Dockerfile
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          build-args: "--build-arg NAMESPACE=testing"
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-testing:
    name: Deploy to Testing
    needs: [ build-testing ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/gateway/gateway/k8s/overlays/testing
      service: gateway
      image: ${{ needs.build-testing.outputs.image }}
      namespace: testing
    secrets: inherit

  build-production:
    name: Build for Production
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    if: github.ref == 'refs/heads/main'
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4

      - name: Build and Push Docker image for Production
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: production
          working-directory: backend/gateway/gateway
          dockerfile: Dockerfile
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          build-args: "--build-arg NAMESPACE=production"
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-production:
    name: Deploy to Production
    needs: [ build-production ]
    permissions:
      contents: read
      id-token: write
    if: github.ref == 'refs/heads/main'
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/gateway/gateway/k8s/overlays/production
      service: gateway
      image: ${{ needs.build-production.outputs.image }}
      namespace: production
    secrets: inherit
