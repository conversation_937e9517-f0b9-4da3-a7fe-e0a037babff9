name: Frontend web CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'frontend/web/**'
      - '.github/workflows/frontend-web.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  SERVICE: web

defaults:
  run:
    working-directory: frontend/web

jobs:
  install:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-node

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: frontend/web/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('frontend/web/pnpm-lock.yaml') }}

  lint:
    needs: [ install ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-node

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: frontend/web/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('frontend/web/pnpm-lock.yaml') }}

      - name: Run ESLint
        run: |
          pnpm lint
          pnpm format:check

  build-testing:
    needs: [ install ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-node

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: frontend/web/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('frontend/web/pnpm-lock.yaml') }}

      - name: Build
        run: |
          pnpm build

      - name: Build and Push Docker image for Testing
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: testing
          working-directory: frontend/web
          dockerfile: Dockerfile
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  build-production:
    needs: [ install ]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-node

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: frontend/web/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('frontend/web/pnpm-lock.yaml') }}

      - name: Build
        run: |
          pnpm build

      - name: Build and Push Docker image for Production
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          namespace: production
          working-directory: frontend/web
          dockerfile: Dockerfile
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-testing:
    needs: [ build-testing ]
    uses: ./.github/workflows/deploy.yml
    permissions:
      contents: read
      id-token: write
    with:
      k8s-path: frontend/web/k8s/overlays/testing
#      service: ${{ needs.build-testing.outputs.service }}
      service: web
      image: ${{ needs.build-testing.outputs.image }}
      namespace: testing
    secrets: inherit

  deploy-production:
    needs: [ build-production ]
    uses: ./.github/workflows/deploy.yml
    permissions:
      contents: read
      id-token: write
    with:
      k8s-path: frontend/web/k8s/overlays/production
#      service: ${{ needs.build-production.outputs.service }}
      service: web
      image: ${{ needs.build-production.outputs.image }}
      namespace: production
    secrets: inherit
