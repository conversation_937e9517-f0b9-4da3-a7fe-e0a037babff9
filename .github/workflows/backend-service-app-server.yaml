name: Backend service-app-server CI/CD

on:
  push:
    branches:
      - main
      - 'dev-*'
    paths:
      - 'backend/service-app/service-app-server/**'
      - 'backend/service-app/service-app-api/**'
      - '.github/workflows/backend-service-app-server.yaml'
  workflow_dispatch: # 允许手动触发

# 每个分支仅允许一个工作流运行
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# 环境变量
env:
  SERVICE: service-app

defaults:
  run:
    working-directory: backend/service-app/service-app-server

jobs:
  compile:
    name: Compile
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Compile
        run: |
          gradle compileJava

  lint:
    name: Lint
    needs: [ compile ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Spotless, SpotBugs & Checkstyle
        run: |
          gradle spotlessCheck spotbugsMain checkstyleMain checkStyleTest

  test:
    name: Test
    needs: [ compile ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Run tests
        run: |
          gradle test

  build-testing:
    name: Build for Testing
    needs: [ compile ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Build Artifact
        run: |
          gradle bootJar

      - name: Build and Push Docker image for Testing
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          working-directory: backend/service-app/service-app-server
          dockerfile: Dockerfile
          namespace: testing
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  build-production:
    name: Build for Production
    if: github.ref == 'refs/heads/main'
    needs: [ compile ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    outputs:
      service: ${{ steps.build_image.outputs.service }}
      image: ${{ steps.build_image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-java

      - name: Build Artifact
        run: |
          gradle bootJar

      - name: Build and Push Docker image for Production
        id: build_image
        uses: ./.github/actions/docker-build-push
        with:
          service: ${{ env.SERVICE }}
          working-directory: backend/service-app/service-app-server
          dockerfile: Dockerfile
          namespace: production
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}
          gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
          gcp-artifact-registry: ${{ vars.GCP_ARTIFACT_REGISTRY }}
          gcp-artifact-registry-docker-repository: ${{ vars.GCP_ARTIFACT_REGISTRY_DOCKER_REPOSITORY }}

  deploy-testing:
    name: Deploy to Testing
    needs: [ build-testing ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/service-app/service-app-server/k8s/overlays/testing
#      service: ${{ needs.build-testing.outputs.service }}
      service: service-app
      image: ${{ needs.build-testing.outputs.image }}
      namespace: testing
    secrets: inherit

  deploy-production:
    name: Deploy to Production
    needs: [ build-production ]
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      k8s-path: backend/service-app/service-app-server/k8s/overlays/production
#      service: ${{ needs.build-production.outputs.service }}
      service: service-app
      image: ${{ needs.build-production.outputs.image }}
      namespace: production
    secrets: inherit
