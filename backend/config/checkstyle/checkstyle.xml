<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
	<property name="charset" value="UTF-8" />
	<property name="severity" value="error" />
	<property name="fileExtensions" value="java" />

	<!-- Excludes all 'module-info.java' files -->
	<module name="BeforeExecutionExclusionFileFilter">
		<property name="fileNamePattern" value="module\-info\.java$" />
	</module>

	<!-- Checks for whitespace -->
	<module name="FileTabCharacter">
		<property name="eachLine" value="true" />
	</module>

	<module name="TreeWalker">

		<module name="MissingSwitchDefault" />

		<!-- Checks for class design -->
		<module name="FinalClass" />
		<module name="InterfaceIsType" />

		<!-- Miscellaneous other checks -->
		<module name="ArrayTypeStyle" />
		<module name="UpperEll" />

		<!-- Disallow other non-jspecify @Nullable annotations -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="jakarta\.annotation\.Nullable|org\.springframework\.lang\.Nullable|org\.jetbrains\.annotations\.Nullable|javax\.annotation\.Nullable|edu\.umd\.cs\.findbugs\.annotations\.Nullable|io\.micrometer\.common\.lang\.Nullable|reactor\.util\.annotation\.Nullable" />
			<property name="message"
				value="Only org.jspecify.annotations.Nullable is allowed for nullable annotations." />
		</module>

		<!-- Disallow other non-jspecify @NonNull annotations -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="jakarta\.annotation\.Nonnull|lombok\.NonNull|org\.springframework\.lang\.NonNull|org\.jetbrains\.annotations\.NotNull|javax\.annotation\.Nonnull|edu\.umd\.cs\.findbugs\.annotations\.NonNull|io\.micrometer\.common\.lang\.NonNull|reactor\.util\.annotation\.NonNull" />
			<property name="message"
				value="Only org.jspecify.annotations.NonNull is allowed for non-null annotations." />
		</module>

		<!-- Disallow Junit 4 -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.junit\.Test|org\.junit\.Before|org\.junit\.After|org\.junit\.BeforeClass|org\.junit\.AfterClass|org\.junit\.Ignore|org\.junit\.runner\.RunWith" />
			<property name="message"
				value="Do not use Junit 4, use Junit 5 instead." />
		</module>

		<!-- Disallow Junit assertions -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.junit\.Assert|org\.junit\.jupiter\.api\.Assertions" />
			<property name="message"
				value="Do not use Junit assertions, use AssertJ instead." />
		</module>

		<!-- Disallow System.out.println -->
		<module name="RegexpSinglelineJava">
			<property name="format" value="System\.out\.println" />
			<property name="message"
				value="Do not use System.out.println, use logger instead." />
		</module>

		<!-- Disallow e.printStackTrace -->
		<module name="RegexpSinglelineJava">
			<property name="format" value="\.printStackTrace\(" />
			<property name="message"
				value="Do not use e.printStackTrace, use logger instead." />
		</module>

		<!-- Disallow Spring @Transactional -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.springframework\.transaction\.annotation\.Transactional" />
			<property name="message"
				value="Do not use Spring @Transactional, use Tx.exec instead." />
		</module>

		<!-- Disallow Spring ApplicationEventPublisher -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.springframework\.context\.ApplicationEventPublisher" />
			<property name="message"
				value="Do not use Spring ApplicationEventPublisher, just write your logic directly." />
		</module>

		<!-- Disallow Spring @Async -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.springframework\.scheduling\.annotation\.Async" />
			<property name="message"
				value="Do not use Spring @Async, use ThreadPool.exec or ThreadPool.submit instead." />
		</module>

		<!-- Disallow Spring @Autowired -->
		<module name="RegexpSinglelineJava">
			<property name="format"
				value="org\.springframework\.beans\.factory\.annotation\.Autowired" />
			<property name="message"
				value="Do not use Spring @Autowired, use constructor injection instead." />
		</module>

	</module>

	<!-- Exclude generated files from checks -->
	<module name="SuppressionFilter">
		<property name="file" value="${config_loc}/suppressions.xml" />
	</module>


</module>
