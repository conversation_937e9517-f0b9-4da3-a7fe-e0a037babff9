dependencies {

    api("org.springframework.boot:spring-boot-starter")
    api("org.springframework.boot:spring-boot-starter-json")
    // Jackson Protobuf support
    // https://github.com/DanielLiu1123/springdoc-bridge/tree/main/jackson-module-protobuf
    api("io.github.danielliu1123:jackson-module-protobuf:${springdocBridgeVersion}")

    compileOnly("com.google.protobuf:protobuf-java")
    compileOnly("com.google.protobuf:protobuf-java-util")

    compileOnly("org.springframework.boot:spring-boot-starter-jdbc")
    compileOnly("org.springframework.boot:spring-boot-starter-web")
    compileOnly("org.springframework.boot:spring-boot-starter-data-redis")
    compileOnly("org.springframework.boot:spring-boot-starter-validation")

    // test
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}
