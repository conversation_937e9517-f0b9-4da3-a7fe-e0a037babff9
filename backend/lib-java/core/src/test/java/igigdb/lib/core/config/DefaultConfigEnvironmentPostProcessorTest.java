package igigdb.lib.core.config;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.apache.commons.logging.Log;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;

/**
 * Tests for {@link DefaultConfigEnvironmentPostProcessor}.
 */
class DefaultConfigEnvironmentPostProcessorTest {

    private DefaultConfigEnvironmentPostProcessor processor;
    private ConfigurableEnvironment environment;
    private Log log;
    private MutablePropertySources propertySources;
    private SpringApplication application;

    @BeforeEach
    void setUp() {
        // Mock DeferredLogFactory and Log
        DeferredLogFactory logFactory = mock(DeferredLogFactory.class);
        log = mock(Log.class);
        when(logFactory.getLog(DefaultConfigEnvironmentPostProcessor.class)).thenReturn(log);

        // Create processor with mocked log factory
        processor = new DefaultConfigEnvironmentPostProcessor(logFactory);

        // Mock environment
        environment = mock(ConfigurableEnvironment.class);
        propertySources = new MutablePropertySources();
        when(environment.getPropertySources()).thenReturn(propertySources);

        // Create a real SpringApplication instance
        application = mock(SpringApplication.class);
    }

    @Test
    void shouldThrowExceptionWhenMultipleProfilesAreActive() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[] {"local", "testing"});

        // When/Then
        assertThatThrownBy(() -> processor.postProcessEnvironment(environment, application))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Only one profile is allowed");
    }

    @Test
    void shouldUseLocalProfileWhenNoProfileIsActive() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[] {});

        // When
        processor.postProcessEnvironment(environment, application);

        // Then
        assertThat(propertySources.size()).isEqualTo(2);

        // Verify log messages
        verify(log).info("Loading profile-specific config 'application-default-config-local.yaml' for profile local");
        verify(log).info("Loading default config 'application-default-config.yaml'");

        // Verify property sources were added in correct order (profile specific first, then default)
        assertThat(propertySources.iterator().next().getName()).isEqualTo("application-default-config.yaml");
        assertThat(propertySources.iterator().next().getProperty("spring.threads.virtual.enabled"))
                .isEqualTo(true);
    }

    @Test
    void shouldUseSpecifiedProfileWhenOneProfileIsActive() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[] {"testing"});

        // When
        processor.postProcessEnvironment(environment, application);

        // Then
        assertThat(propertySources.size()).isEqualTo(2);

        // Verify log messages
        verify(log)
                .info("Loading profile-specific config 'application-default-config-testing.yaml' for profile testing");
        verify(log).info("Loading default config 'application-default-config.yaml'");

        // Verify property sources were added in correct order (profile specific first, then default)
        assertThat(propertySources.iterator().next().getName()).isEqualTo("application-default-config.yaml");
    }

    @Test
    void shouldUseLocalProfileWhenNonStandardProfileIsActive() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[] {"dev"});

        // When
        processor.postProcessEnvironment(environment, application);

        // Then
        assertThat(propertySources.size()).isEqualTo(2);

        // Verify log messages
        verify(log).info("Loading profile-specific config 'application-default-config-local.yaml' for profile local");
        verify(log).info("Loading default config 'application-default-config.yaml'");

        // Verify property sources were added in correct order (profile specific first, then default)
        assertThat(propertySources.iterator().next().getName()).isEqualTo("application-default-config.yaml");
    }
}
