package igigdb.lib.core.json;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
public final class LongToStringModule extends SimpleModule {

    public static final LongToStringModule INSTANCE = new LongToStringModule();

    private LongToStringModule() {
        addSerializer(Long.class, ToStringSerializer.instance);
        addSerializer(Long.TYPE, ToStringSerializer.instance);
        addSerializer(BigInteger.class, ToStringSerializer.instance);
    }
}
