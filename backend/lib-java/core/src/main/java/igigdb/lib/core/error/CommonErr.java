package igigdb.lib.core.error;

/**
 * Common error codes used across the application.
 * This enum implements the ErrCode interface to provide a consistent structure for error codes.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public enum CommonErr implements ErrCode {

    // System level errors (1000-1999)
    SYSTEM_ERROR("SYSTEM_ERROR", "System error occurred", 500),
    SERVICE_UNAVAILABLE("SERVICE_UNAVAILABLE", "Service is currently unavailable", 503),
    TIMEOUT("TIMEOUT", "Request timed out", 504),

    // Validation errors (2000-2999)
    INVALID_PARAMETER("INVALID_PARAMETER", "Invalid parameter", 400),
    MISSING_PARAMETER("MISSING_PARAMETER", "Required parameter is missing", 400),
    INVALID_FORMAT("INVALID_FORMAT", "Invalid data format", 400),

    // Authentication/Authorization errors (3000-3999)
    UNAUTHORIZED("UNAUTHORIZED", "Unauthorized access", 401),
    FORBIDDEN("FORBIDDEN", "Access forbidden", 403),
    TOKEN_EXPIRED("TOKEN_EXPIRED", "Token has expired", 401),
    INVALID_TOKEN("INVALID_TOKEN", "Invalid token", 401),

    // Resource errors (4000-4999)
    RESOURCE_NOT_FOUND("RESOURCE_NOT_FOUND", "Resource not found", 404),
    RESOURCE_ALREADY_EXISTS("RESOURCE_ALREADY_EXISTS", "Resource already exists", 409),
    RESOURCE_CONFLICT("RESOURCE_CONFLICT", "Resource conflict", 409),

    // Business logic errors (5000-5999)
    OPERATION_FAILED("OPERATION_FAILED", "Operation failed", 400),
    BUSINESS_RULE_VIOLATION("BUSINESS_RULE_VIOLATION", "Business rule violation", 400),

    // External service errors (6000-6999)
    EXTERNAL_SERVICE_ERROR("EXTERNAL_SERVICE_ERROR", "External service error", 502),
    EXTERNAL_SERVICE_TIMEOUT("EXTERNAL_SERVICE_TIMEOUT", "External service timeout", 504),

    // Data errors (7000-7999)
    DATA_INTEGRITY_ERROR("DATA_INTEGRITY_ERROR", "Data integrity error", 400),
    DATA_ACCESS_ERROR("DATA_ACCESS_ERROR", "Data access error", 500);

    private final String code;
    private final String message;
    private final int status;

    CommonErr(String code, String message, int status) {
        this.code = code;
        this.message = message;
        this.status = status;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public int getStatus() {
        return status;
    }
}
