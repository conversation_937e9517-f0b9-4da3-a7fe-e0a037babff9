package igigdb.lib.core.profile;

import java.util.Arrays;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Configuration(proxyBeanMethods = false)
public class ProfileConfiguration {

    @Bean
    public Profile igigdbProfile(Environment environment) {
        return getProfile(environment);
    }

    private static Profile getProfile(Environment environment) {
        var profiles = Arrays.asList(environment.getActiveProfiles());
        if (profiles.size() > 1) {
            throw new IllegalArgumentException("Only one profile is allowed, but got " + profiles);
        }
        return profiles.stream().findFirst().map(Profile::tryParse).orElse(Profile.LOCAL);
    }
}
