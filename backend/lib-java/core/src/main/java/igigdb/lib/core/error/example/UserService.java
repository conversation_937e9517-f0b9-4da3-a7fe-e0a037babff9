package igigdb.lib.core.error.example;

import igigdb.lib.core.error.BizException;

/**
 * Example service class demonstrating how to use BizException.
 * This is for demonstration purposes only.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public class UserService {

    /**
     * Example method demonstrating how to throw a BizException.
     *
     * @param userId the user ID
     * @return the user
     * @throws BizException if the user is not found
     */
    public User getUserById(String userId) {
        // Simulate user lookup
        User user = findUserById(userId);

        if (user == null) {
            // Throw a BizException with a specific error code
            throw BizException.of(UserErrorCode.USER_NOT_FOUND).addData("userId", userId);
        }

        return user;
    }

    /**
     * Example method demonstrating how to throw a BizException with a custom message.
     *
     * @param username the username
     * @param password the password
     * @return the user
     * @throws BizException if the credentials are invalid
     */
    public User login(String username, String password) {
        // Simulate authentication
        User user = authenticate(username, password);

        if (user == null) {
            // Throw a BizException with a custom message
            throw BizException.of(UserErrorCode.INVALID_USERNAME, "Username or password is incorrect");
        }

        if (user.isLocked()) {
            // Throw a BizException with additional data
            throw BizException.of(UserErrorCode.ACCOUNT_LOCKED)
                    .addData("lockedUntil", user.getLockedUntil())
                    .addData("reason", user.getLockReason());
        }

        return user;
    }

    // Simulated methods
    private User findUserById(String userId) {
        // In a real application, this would query a database
        return null;
    }

    private User authenticate(String username, String password) {
        // In a real application, this would verify credentials
        return null;
    }

    // Simple User class for the example
    public static class User {
        private String id;
        private String username;
        private boolean locked;
        private String lockedUntil;
        private String lockReason;

        public String getId() {
            return id;
        }

        public String getUsername() {
            return username;
        }

        public boolean isLocked() {
            return locked;
        }

        public String getLockedUntil() {
            return lockedUntil;
        }

        public String getLockReason() {
            return lockReason;
        }
    }
}
