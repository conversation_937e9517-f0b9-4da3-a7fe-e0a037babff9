package igigdb.lib.core.util;

import igigdb.lib.core.context.ContextualExecutorService;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public final class ThreadPool {

    private ThreadPool() {
        throw new IllegalStateException("Utility class");
    }

    private static final ExecutorService pool =
            new ContextualExecutorService(Executors.newVirtualThreadPerTaskExecutor());

    /**
     * Returns the shared thread pool.
     *
     * @return the shared thread pool
     */
    public static ExecutorService getPool() {
        return pool;
    }

    public static void shutdown() {
        pool.shutdown();
    }

    public static void exec(Runnable task) {
        pool.execute(task);
    }

    public static <T> CompletableFuture<T> submit(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, pool);
    }
}
