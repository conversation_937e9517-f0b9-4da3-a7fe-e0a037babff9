package igigdb.lib.core.health;

import java.util.List;
import org.jspecify.annotations.Nullable;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public class HealthService {

    private final List<HealthChecker> healthCheckers;

    public HealthService(List<HealthChecker> healthCheckers) {
        this.healthCheckers = healthCheckers;
    }

    public boolean check(@Nullable String service) {
        return switch (service) {
            case null -> checkAllServices();
            case String s when s.isBlank() -> checkAllServices();
            case "startup", "readiness" -> checkAllServices();
            case "liveness" -> true;
            default -> checkOneService(service);
        };
    }

    private boolean checkAllServices() {
        for (HealthChecker healthChecker : healthCheckers) {
            if (!healthChecker.check()) {
                return false;
            }
        }
        return true;
    }

    private boolean checkOneService(String service) {
        for (HealthChecker healthChecker : healthCheckers) {
            if (healthChecker.service().equals(service)) {
                return healthChecker.check();
            }
        }
        return false;
    }
}
