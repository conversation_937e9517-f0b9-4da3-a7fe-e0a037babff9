package igigdb.lib.core.util;

import java.util.function.Supplier;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.function.SingletonSupplier;

/**
 * Transaction utility class, use to execute code in a transaction, replacement of {@code @Transactional}.
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
public final class Tx {

    private static final SingletonSupplier<TransactionOperations> transactionOperations = SingletonSupplier.of(() ->
            SpringUtil.getContext().getBeanProvider(TransactionOperations.class).getIfUnique());

    private Tx() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Execute the runnable in a transaction.
     *
     * @param runnable runnable
     */
    public static void exec(Runnable runnable) {
        var tx = transactionOperations.get();
        if (tx != null) {
            tx.executeWithoutResult(__ -> runnable.run());
        } else {
            runnable.run();
        }
    }

    /**
     * Execute the supplier in a transaction and return the result.
     *
     * @param supplier supplier
     * @param <T>      return type
     * @return result
     */
    public static <T> T exec(Supplier<T> supplier) {
        var tx = transactionOperations.get();
        if (tx != null) {
            return tx.execute(__ -> supplier.get());
        } else {
            return supplier.get();
        }
    }
}
