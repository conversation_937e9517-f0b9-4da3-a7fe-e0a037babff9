package igigdb.lib.core.health;

import java.util.List;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
@Configuration(proxyBeanMethods = false)
public class HealthConfiguration {

    @Bean
    public HealthService healthService(List<HealthChecker> healthCheckers) {
        return new HealthService(healthCheckers);
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({javax.sql.DataSource.class})
    static class DataSource {

        @Bean
        public DataSourceHealthChecker dataSourceHealthChecker(List<javax.sql.DataSource> dataSources) {
            return new DataSourceHealthChecker(dataSources);
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({RedisConnectionFactory.class})
    static class Redis {

        @Bean
        public RedisHealthChecker redisHealthChecker(List<RedisConnectionFactory> redisConnectionFactories) {
            return new RedisHealthChecker(redisConnectionFactories);
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnWebApplication
    static class Web {

        @Bean
        public HealthController healthController(HealthService healthService) {
            return new HealthController(healthService);
        }
    }
}
