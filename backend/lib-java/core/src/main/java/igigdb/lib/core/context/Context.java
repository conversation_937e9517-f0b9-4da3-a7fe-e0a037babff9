package igigdb.lib.core.context;

import java.util.List;
import org.jspecify.annotations.Nullable;
import org.springframework.util.LinkedCaseInsensitiveMap;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public final class Context {

    private static final InheritableThreadLocal<Context> CONTEXT = new InheritableThreadLocal<>();

    private final LinkedCaseInsensitiveMap<List<String>> headers = new LinkedCaseInsensitiveMap<>();

    public void addHeader(String key, List<String> value) {
        headers.put(key, value);
    }

    public LinkedCaseInsensitiveMap<List<String>> getHeaders() {
        return headers.clone();
    }

    public static Context mustGet() {
        var ctx = get();
        if (ctx == null) {
            throw new IllegalStateException("Context is not initialized");
        }
        return ctx;
    }

    @Nullable
    public static Context get() {
        return CONTEXT.get();
    }

    public static void set(Context ctx) {
        CONTEXT.set(ctx);
    }

    public static void remove() {
        CONTEXT.remove();
    }
}
