package igigdb.lib.core;

import igigdb.lib.core.cds.CDSConfiguration;
import igigdb.lib.core.context.ContextConfiguration;
import igigdb.lib.core.health.HealthConfiguration;
import igigdb.lib.core.json.JsonConfiguration;
import igigdb.lib.core.profile.ProfileConfiguration;
import igigdb.lib.core.util.SpringUtil;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @since 2025/4/29
 */
@AutoConfiguration
@Import({
    HealthConfiguration.class,
    ContextConfiguration.class,
    ProfileConfiguration.class,
    CDSConfiguration.class,
    JsonConfiguration.class
})
public class IgigdbAutoConfiguration {

    public IgigdbAutoConfiguration(ApplicationContext applicationContext) {
        SpringUtil.setContext(applicationContext);
    }
}
