package igigdb.lib.core.error;

/**
 * Interface for error codes in the application.
 * All error codes should implement this interface to ensure a consistent structure.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public interface ErrCode {

    /**
     * Returns the error code.
     * The code should be a unique identifier for the error.
     * Recommended format: MODULE_ERROR_TYPE, e.g., USER_NOT_FOUND
     *
     * @return the error code
     */
    String getCode();

    /**
     * Returns the error message.
     * The message should be a human-readable description of the error.
     *
     * @return the error message
     */
    String getMessage();

    /**
     * Returns the HTTP status code associated with this error.
     * This is useful for mapping business errors to appropriate HTTP responses.
     *
     * @return the HTTP status code
     */
    default int getStatus() {
        return 400; // Default to Bad Request
    }
}
