package igigdb.lib.core.health;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public class RedisHealthChecker implements HealthChecker {

    private static final Logger log = LoggerFactory.getLogger(RedisHealthChecker.class);
    private final List<RedisConnectionFactory> redisConnectionFactories;

    public RedisHealthChecker(List<RedisConnectionFactory> redisConnectionFactories) {
        this.redisConnectionFactories = redisConnectionFactories;
    }

    @Override
    public String service() {
        return "Redis";
    }

    @Override
    public boolean check() {
        for (var redisConnectionFactory : redisConnectionFactories) {
            try (var conn = redisConnectionFactory.getConnection()) {
                conn.ping();
            } catch (Exception e) {
                log.error("Redis ping failed", e);
                return false;
            }
        }
        return true;
    }
}
