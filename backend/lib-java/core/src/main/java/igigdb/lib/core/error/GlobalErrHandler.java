package igigdb.lib.core.error;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Global exception handler for REST APIs.
 * This class handles exceptions thrown by controllers and converts them to appropriate HTTP responses.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
@RestControllerAdvice
public class GlobalErrHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalErrHandler.class);

    /**
     * Handles BizException.
     *
     * @param e the exception
     * @return the error response
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<ErrResp> handleBizException(BizException e) {
        log.warn("Business exception: {}", e.getMessage());
        ErrResp errorResponse = ErrResp.of(e);
        return ResponseEntity.status(e.getErrorCode().getStatus()).body(errorResponse);
    }

    /**
     * Handles MethodArgumentNotValidException.
     * This exception is thrown when @Valid validation fails on a method argument.
     *
     * @param e the exception
     * @return the error response
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrResp handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        return ErrResp.of(CommonErr.INVALID_PARAMETER).addData("fields", errors);
    }

    /**
     * Handles ConstraintViolationException.
     * This exception is thrown when @Validated validation fails.
     *
     * @param e the exception
     * @return the error response
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrResp handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("Constraint violation: {}", e.getMessage());
        Map<String, String> errors = e.getConstraintViolations().stream()
                .collect(Collectors.toMap(
                        violation -> violation.getPropertyPath().toString(),
                        ConstraintViolation::getMessage,
                        (error1, error2) -> error1));

        return ErrResp.of(CommonErr.INVALID_PARAMETER).addData("fields", errors);
    }

    /**
     * Handles all other exceptions.
     *
     * @param e the exception
     * @return the error response
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrResp handleException(Exception e) {
        log.error("Unhandled exception", e);
        return ErrResp.of(CommonErr.SYSTEM_ERROR);
    }
}
