package igigdb.lib.core.context.web;

import igigdb.lib.core.context.Context;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public final class ContextualOncePerRequestFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        Context.set(buildContext(request));
        try {
            filterChain.doFilter(request, response);
        } finally {
            Context.remove();
        }
    }

    private static Context buildContext(HttpServletRequest request) {
        var ctx = new Context();

        var names = request.getHeaderNames();
        while (names.hasMoreElements()) {
            var name = names.nextElement();
            ctx.addHeader(name, getValues(request, name));
        }

        return ctx;
    }

    private static ArrayList<String> getValues(HttpServletRequest request, String headerName) {
        var result = new ArrayList<String>();
        var values = request.getHeaders(headerName);
        while (values.hasMoreElements()) {
            result.add(values.nextElement());
        }
        return result;
    }
}
