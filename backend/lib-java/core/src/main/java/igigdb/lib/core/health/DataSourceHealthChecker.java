package igigdb.lib.core.health;

import java.sql.SQLException;
import java.util.List;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
public class DataSourceHealthChecker implements HealthChecker {
    private static final Logger log = LoggerFactory.getLogger(DataSourceHealthChecker.class);

    private final List<DataSource> dataSources;

    public DataSourceHealthChecker(List<DataSource> dataSources) {
        this.dataSources = dataSources;
    }

    @Override
    public String service() {
        return "DataSource";
    }

    @Override
    public boolean check() {
        for (var dataSource : dataSources) {
            try (var connection = dataSource.getConnection()) {
                if (!connection.isValid(2)) {
                    log.warn("{} connection is not valid", dataSource);
                    return false;
                }
            } catch (SQLException e) {
                log.error("DataSource connection validation failed", e);
                return false;
            }
        }
        return true;
    }
}
