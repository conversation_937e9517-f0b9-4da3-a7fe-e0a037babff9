package igigdb.lib.core.util;

import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @since 2025/4/29
 */
public final class SpringUtil {

    private SpringUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static ApplicationContext ctx;

    public static void setContext(ApplicationContext applicationContext) {
        SpringUtil.ctx = applicationContext;
    }

    /**
     * Get the application context.
     *
     * @return the application context
     */
    public static ApplicationContext getContext() {
        if (ctx == null) {
            throw new IllegalStateException("You must in the Spring environment to use this method!");
        }
        return ctx;
    }
}
