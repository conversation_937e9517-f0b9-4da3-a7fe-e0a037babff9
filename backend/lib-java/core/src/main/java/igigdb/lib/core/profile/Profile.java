package igigdb.lib.core.profile;

import org.jspecify.annotations.Nullable;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
public enum Profile {
    LOCAL("local"),
    TESTING("test"),
    PRODUCTION("prod");

    private final String profile;

    Profile(String profile) {
        this.profile = profile;
    }

    public String getProfile() {
        return profile;
    }

    @Nullable
    public static Profile tryParse(String profile) {
        for (var p : Profile.values()) {
            if (profile.equalsIgnoreCase(p.getProfile())) {
                return p;
            }
        }
        return null;
    }
}
