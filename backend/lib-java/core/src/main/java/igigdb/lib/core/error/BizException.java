package igigdb.lib.core.error;

import java.util.HashMap;
import java.util.Map;

/**
 * Business exception class for handling application-specific errors.
 * This exception is used to represent errors that occur during business logic execution.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public class BizException extends RuntimeException {

    private final ErrCode errorCode;
    private final Map<String, Object> data;

    /**
     * Constructs a new business exception with the specified error code.
     *
     * @param errorCode the error code
     */
    public BizException(ErrCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.data = new HashMap<>();
    }

    /**
     * Constructs a new business exception with the specified error code and message.
     *
     * @param errorCode the error code
     * @param message   the detail message
     */
    public BizException(ErrCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.data = new HashMap<>();
    }

    /**
     * Constructs a new business exception with the specified error code, message, and cause.
     *
     * @param errorCode the error code
     * @param message   the detail message
     * @param cause     the cause of the exception
     */
    public BizException(ErrCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.data = new HashMap<>();
    }

    /**
     * Constructs a new business exception with the specified error code and cause.
     *
     * @param errorCode the error code
     * @param cause     the cause of the exception
     */
    public BizException(ErrCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.data = new HashMap<>();
    }

    /**
     * Returns the error code associated with this exception.
     *
     * @return the error code
     */
    public ErrCode getErrorCode() {
        return errorCode;
    }

    /**
     * Returns the error code value as a string.
     *
     * @return the error code value
     */
    public String getCode() {
        return errorCode.getCode();
    }

    /**
     * Adds additional data to the exception.
     *
     * @param key   the data key
     * @param value the data value
     * @return this exception instance for method chaining
     */
    public BizException addData(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    /**
     * Returns the additional data associated with this exception.
     *
     * @return the additional data
     */
    public Map<String, Object> getData() {
        return new HashMap<>(data);
    }

    /**
     * Creates a new business exception with the specified error code.
     *
     * @param errorCode the error code
     * @return a new business exception
     */
    public static BizException of(ErrCode errorCode) {
        return new BizException(errorCode);
    }

    /**
     * Creates a new business exception with the specified error code and message.
     *
     * @param errorCode the error code
     * @param message   the detail message
     * @return a new business exception
     */
    public static BizException of(ErrCode errorCode, String message) {
        return new BizException(errorCode, message);
    }

    /**
     * Creates a new business exception with the specified error code and cause.
     *
     * @param errorCode the error code
     * @param cause     the cause of the exception
     * @return a new business exception
     */
    public static BizException of(ErrCode errorCode, Throwable cause) {
        return new BizException(errorCode, cause);
    }

    /**
     * Creates a new business exception with the specified error code, message, and cause.
     *
     * @param errorCode the error code
     * @param message   the detail message
     * @param cause     the cause of the exception
     * @return a new business exception
     */
    public static BizException of(ErrCode errorCode, String message, Throwable cause) {
        return new BizException(errorCode, message, cause);
    }
}
