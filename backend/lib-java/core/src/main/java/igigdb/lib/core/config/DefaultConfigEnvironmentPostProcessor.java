package igigdb.lib.core.config;

import igigdb.lib.core.profile.Profile;
import java.util.Arrays;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
public class DefaultConfigEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private final Log log;

    public DefaultConfigEnvironmentPostProcessor(DeferredLogFactory logFactory) {
        this.log = logFactory.getLog(getClass());
    }

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {

        var profile = getProfile(environment);

        // profile specific config should be loaded before default config
        // Use a switch statement to prevent forgetting to update this code when a new Profile is added.
        var configName =
                switch (profile) {
                    case LOCAL -> "application-default-config-local.yaml";
                    case TESTING -> "application-default-config-testing.yaml";
                    case PRODUCTION -> "application-default-config-production.yaml";
                };
        var profileResource = new ClassPathResource(configName);
        if (profileResource.exists()) {
            environment.getPropertySources().addFirst(getPropertySource(profileResource));
            log.info("Loading profile-specific config '%s' for profile %s"
                    .formatted(profileResource.getFilename(), profile.getProfile()));
        }

        // load default config
        var defaultResource = new ClassPathResource("application-default-config.yaml");
        if (defaultResource.exists()) {
            environment.getPropertySources().addFirst(getPropertySource(defaultResource));
            log.info("Loading default config '%s'".formatted(defaultResource.getFilename()));
        }
    }

    private static Profile getProfile(Environment environment) {
        var profiles = Arrays.asList(environment.getActiveProfiles());
        if (profiles.size() > 1) {
            throw new IllegalArgumentException("Only one profile is allowed, but got " + profiles);
        }
        return profiles.stream().findFirst().map(Profile::tryParse).orElse(Profile.LOCAL);
    }

    private PropertySource<?> getPropertySource(Resource resource) {
        String filename = resource.getFilename();
        if (!resource.exists()) {
            log.warn(filename + " doesn't exist");
        }

        // parse yaml
        var yamlPropertiesFactoryBean = new YamlPropertiesFactoryBean();
        yamlPropertiesFactoryBean.setResources(resource);

        var properties = yamlPropertiesFactoryBean.getObject();

        assert filename != null;
        assert properties != null;
        return new PropertiesPropertySource(filename, properties);
    }
}
