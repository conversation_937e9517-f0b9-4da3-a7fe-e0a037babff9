package igigdb.lib.core.error;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Standard error response for REST APIs.
 * This class provides a consistent structure for error responses across the application.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public class ErrResp {

    private final String code;
    private final String message;
    private final LocalDateTime timestamp;
    private final Map<String, Object> data;

    /**
     * Constructs a new error response with the specified error code and message.
     *
     * @param code    the error code
     * @param message the error message
     */
    public ErrResp(String code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = LocalDateTime.now();
        this.data = new HashMap<>();
    }

    /**
     * Constructs a new error response from an ErrCode.
     *
     * @param errorCode the error code
     */
    public ErrResp(ErrCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.timestamp = LocalDateTime.now();
        this.data = new HashMap<>();
    }

    /**
     * Constructs a new error response from a BizException.
     *
     * @param exception the business exception
     */
    public ErrResp(BizException exception) {
        this.code = exception.getCode();
        this.message = exception.getMessage();
        this.timestamp = LocalDateTime.now();
        this.data = exception.getData();
    }

    /**
     * Returns the error code.
     *
     * @return the error code
     */
    public String getCode() {
        return code;
    }

    /**
     * Returns the error message.
     *
     * @return the error message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Returns the timestamp when the error occurred.
     *
     * @return the timestamp
     */
    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    /**
     * Returns the additional data associated with this error.
     *
     * @return the additional data
     */
    public Map<String, Object> getData() {
        return data;
    }

    /**
     * Adds additional data to the error response.
     *
     * @param key   the data key
     * @param value the data value
     * @return this error response instance for method chaining
     */
    public ErrResp addData(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    /**
     * Creates a new error response from an ErrorCode.
     *
     * @param errorCode the error code
     * @return a new error response
     */
    public static ErrResp of(ErrCode errorCode) {
        return new ErrResp(errorCode);
    }

    /**
     * Creates a new error response from a BizException.
     *
     * @param exception the business exception
     * @return a new error response
     */
    public static ErrResp of(BizException exception) {
        return new ErrResp(exception);
    }

    /**
     * Creates a new error response with the specified error code and message.
     *
     * @param code    the error code
     * @param message the error message
     * @return a new error response
     */
    public static ErrResp of(String code, String message) {
        return new ErrResp(code, message);
    }
}
