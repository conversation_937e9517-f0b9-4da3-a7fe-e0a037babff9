package igigdb.lib.core.json;

import jacksonmodule.protobuf.ProtobufModule;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Configuration(proxyBeanMethods = false)
public class JsonConfiguration {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer igigdbJackson2ObjectMapperBuilderCustomizer() {
        return builder -> builder.modules(modules -> {
            if (modules.stream().noneMatch(c -> c instanceof ProtobufModule)) {
                modules.add(new ProtobufModule());
            }
            if (modules.stream().noneMatch(c -> c instanceof LongToStringModule)) {
                modules.add(LongToStringModule.INSTANCE);
            }
        });
    }
}
