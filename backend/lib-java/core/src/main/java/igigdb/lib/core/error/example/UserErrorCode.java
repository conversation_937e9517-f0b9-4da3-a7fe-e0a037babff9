package igigdb.lib.core.error.example;

import igigdb.lib.core.error.ErrCode;

/**
 * Example of domain-specific error codes for the User domain.
 * This enum demonstrates how to create domain-specific error codes.
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
public enum UserErrorCode implements ErrCode {
    USER_NOT_FOUND("USER_NOT_FOUND", "User not found", 404),
    USER_ALREADY_EXISTS("USER_ALREADY_EXISTS", "User already exists", 409),
    INVALID_USERNAME("INVALID_USERNAME", "Invalid username format", 400),
    INVALID_PASSWORD("INVALID_PASSWORD", "Invalid password format", 400),
    ACCOUNT_LOCKED("ACCOUNT_LOCKED", "Account is locked", 403),
    ACCOUNT_DISABLED("ACCOUNT_DISABLED", "Account is disabled", 403);

    private final String code;
    private final String message;
    private final int status;

    UserErrorCode(String code, String message, int status) {
        this.code = code;
        this.message = message;
        this.status = status;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public int getStatus() {
        return status;
    }
}
