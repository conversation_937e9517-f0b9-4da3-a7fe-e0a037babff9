package igigdb.lib.mybatis.typehandler;

import static java.util.stream.Collectors.toMap;

import com.google.protobuf.ProtocolMessageEnum;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.core.ResolvableType;

/**
 * 将数据库 string 类型转换为 Protobuf enum，使用 enum name 作为序列化和反序列化。
 *
 * <AUTHOR>
 * @since 2025/4/26
 */
public abstract class BaseProtobufEnumTypeHandler<T extends Enum<T> & ProtocolMessageEnum> extends BaseTypeHandler<T> {

    private static final String UNRECOGNIZED = "UNRECOGNIZED";

    @SuppressWarnings("rawtypes")
    private final Map<String, Enum> nameToEnum;

    private final Enum<?> defaultEnum;
    private final Enum<?> unrecognizedEnum;

    @SuppressFBWarnings("CT_CONSTRUCTOR_THROW")
    protected BaseProtobufEnumTypeHandler() {
        var clz = ResolvableType.forClass(getClass())
                .as(BaseProtobufEnumTypeHandler.class)
                .resolveGeneric(0);
        this.nameToEnum = Optional.ofNullable(clz).map(Class::getEnumConstants).stream()
                .flatMap(Arrays::stream)
                .map(Enum.class::cast)
                .filter(e -> !Objects.equals(e.name(), UNRECOGNIZED))
                .collect(toMap(Enum::name, Function.identity(), (o, n) -> o, LinkedHashMap::new));
        this.defaultEnum = Optional.ofNullable(clz).map(Class::getEnumConstants).stream()
                .flatMap(Arrays::stream)
                .map(ProtocolMessageEnum.class::cast)
                .filter(e -> e.getNumber() == 0)
                .map(Enum.class::cast)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Protobuf enum does not have a value with number 0"));
        this.unrecognizedEnum = Optional.of(clz).map(Class::getEnumConstants).stream()
                .flatMap(Arrays::stream)
                .map(Enum.class::cast)
                .filter(e -> Objects.equals(e.name(), UNRECOGNIZED))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Protobuf enum should have an UNRECOGNIZED value"));
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.name());
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return fromString(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return fromString(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return fromString(cs.getString(columnIndex));
    }

    @SuppressWarnings("unchecked")
    /*private*/ T fromString(String enumName) {
        if (enumName == null || enumName.isBlank()) {
            return (T) defaultEnum;
        }
        var result = (T) nameToEnum.get(enumName);
        if (result == null) {
            return (T) unrecognizedEnum;
        }
        return result;
    }
}
