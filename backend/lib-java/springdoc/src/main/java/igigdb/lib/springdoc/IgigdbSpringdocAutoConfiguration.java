package igigdb.lib.springdoc;

import java.math.BigInteger;
import org.springdoc.core.utils.SpringDocUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;

/**
 * <AUTHOR>
 * @since 2025/6/8
 */
@AutoConfiguration
public class IgigdbSpringdocAutoConfiguration {

    public IgigdbSpringdocAutoConfiguration() {
        // Write long as string
        // see igigdb.lib.core.json.JsonConfiguration.igigdbJackson2ObjectMapperBuilderCustomizer
        SpringDocUtils.getConfig()
                .replaceWithClass(Long.class, String.class)
                .replaceWithClass(Long.TYPE, String.class)
                .replaceWithClass(BigInteger.class, String.class);
    }
}
