group=igigdb

# https://github.com/spring-projects/spring-boot
springBootVersion=3.5.4
# https://docs.spring.io/spring-cloud-release/reference/index.html
springCloudVersion=2025.0.0
# https://github.com/spring-gradle-plugins/dependency-management-plugin
springDependencyManagementVersion=1.1.7

# gRPC related
# https://github.com/google/protobuf-gradle-plugin
protobufGradlePluginVersion=0.9.5
# https://github.com/DanielLiu1123/grpc-starter
grpcStarterVersion=3.5.4
# https://github.com/DanielLiu1123/grpc-starter/blob/main/gradle.properties#L21
grpcVersion=1.73.0
# https://github.com/danielliu1123/grpc-starter/blob/main/gradle.properties#L25
protobufVersion=4.31.1

# openapi docs
# https://github.com/springdoc/springdoc-openapi
springdocVersion=2.8.9
# https://github.com/DanielLiu1123/springdoc-bridge
springdocBridgeVersion=0.3.5

# https://github.com/kimicla/mybatis-generator-plugin
mybatisGeneratorGradlePlugin=2.5
# https://github.com/mybatis/generator
mybatisGeneratorCoreVersion=1.4.2
# https://github.com/mybatis/spring-boot-starter
mybatisBootStarterVersion=3.0.5
# https://github.com/mybatis/mybatis-dynamic-sql
mybatisDynamicSqlVersion=1.5.2

# For data crawling
# https://github.com/microsoft/playwright-java
# Playwright for Java - modern web automation library
playwrightVersion=1.54.0

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.2.1
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.1.13
# https://github.com/spotbugs/spotbugs-gradle-plugin/blob/master/build.gradle.kts
spotbugsAnnotationsVersion=4.8.6
# https://github.com/checkstyle/checkstyle
checkstyleVersion=10.26.1

org.gradle.jvmargs=-Xmx4g
org.gradle.parallel=true
org.gradle.caching=true
# https://docs.gradle.org/9.0.0/userguide/configuration_cache_enabling.html#config_cache:usage:enable
org.gradle.configuration-cache=true
