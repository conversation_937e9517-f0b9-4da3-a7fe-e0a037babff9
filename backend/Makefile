init:
	bash ./init.sh

gen:
	@echo "Generating code with buf..."
	@echo "Cleaning existing generated code..."
	rm -rf gen/java gen/go
	@echo "Generating protobuf code..."
	cd proto && buf generate
	@echo "Setting up build files for generated code..."
	$(MAKE) setup-gen-dirs

setup-gen-dirs:
	@echo "Setting up Java gen directory..."
	@if [ -d "gen/java" ]; then \
		cp templates/build.gradle gen/java/build.gradle; \
		echo "Created gen/java/build.gradle"; \
	fi
	@echo "Setting up Go gen directory..."
	@if [ -d "gen/go" ]; then \
		cp templates/go.mod gen/go/go.mod; \
		echo "Created gen/go/go.mod"; \
	fi
	@echo "Generated code setup complete!"

clean-gen:
	@echo "Cleaning generated code..."
	rm -rf gen/java gen/go
	@echo "Generated code cleaned!"

.PHONY: init gen setup-gen-dirs clean-gen