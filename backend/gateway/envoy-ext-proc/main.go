package main

import (
	"flag"
	"fmt"
	_ "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	extproc "github.com/envoyproxy/go-control-plane/envoy/service/ext_proc/v3"
	"github.com/igigdb/igigdb/backend/gateway/envoy-ext-proc/pkg/client"
	"github.com/igigdb/igigdb/backend/gateway/envoy-ext-proc/pkg/server"
	"google.golang.org/grpc"
	"log"
	"net"
)

var (
	port = flag.Int("port", 9090, "gRPC server port")
)

func main() {
	flag.Parse()

	client.InitClient()

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", *port))
	if err != nil {
		log.Fatalf("无法监听端口 %d: %v", *port, err)
	}

	grpcServer := grpc.NewServer()
	extproc.RegisterExternalProcessorServer(grpcServer, server.NewExtProcServer())

	log.Printf("ext-proc 服务器正在监听端口 %d", *port)
	if err := grpcServer.Serve(lis); err != nil {
		log.Fatalf("无法启动 gRPC 服务器: %v", err)
	}
}
