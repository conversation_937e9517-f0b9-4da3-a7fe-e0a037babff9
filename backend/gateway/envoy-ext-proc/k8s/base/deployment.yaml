apiVersion: apps/v1
kind: Deployment
metadata:
  name: envoy-ext-proc
  labels:
    service: envoy-ext-proc
    branch: ${BRANCH}
    commit: ${COMMIT}
    datetime: ${DATETIME}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      service: envoy-ext-proc
      branch: ${BRANCH}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        service: envoy-ext-proc
        branch: ${BRANCH}
        commit: ${COMMIT}
        datetime: ${DATETIME}
    spec:
      containers:
        - name: envoy-ext-proc
          image: ${IMAGE}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: 50m
              memory: 50Mi
            limits:
              cpu: 2000m
              memory: 512Mi
          ports:
            - name: grpc
              containerPort: 9090
          startupProbe:
            tcpSocket:
              port: 9090
            initialDelaySeconds: 5
            periodSeconds: 2
            timeoutSeconds: 1
            failureThreshold: 30
            successThreshold: 1
          readinessProbe:
            tcpSocket:
              port: 9090
            initialDelaySeconds: 5
            periodSeconds: 2
            timeoutSeconds: 1
            failureThreshold: 2
            successThreshold: 1
          livenessProbe:
            tcpSocket:
              port: 9090
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 2
            successThreshold: 1
