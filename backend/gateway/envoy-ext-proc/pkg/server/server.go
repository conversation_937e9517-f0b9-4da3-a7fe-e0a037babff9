package server

import (
	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	extproc "github.com/envoyproxy/go-control-plane/envoy/service/ext_proc/v3"
	typev3 "github.com/envoyproxy/go-control-plane/envoy/type/v3"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"io"
	"log"
	"strings"
)

const XRouteName = "x-route-name"

// ExtProcServer 实现 ext_proc.ExternalProcessorServer 接口
type ExtProcServer struct {
	extproc.UnimplementedExternalProcessorServer
}

func NewExtProcServer() *ExtProcServer {
	return &ExtProcServer{}
}

// Process 处理来自 Envoy 的请求
func (s *ExtProcServer) Process(stream extproc.ExternalProcessor_ProcessServer) error {

	// see https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/filters/http/ext_proc/v3/processing_mode.proto#extensions-filters-http-ext-proc-v3-processingmode
	// 默认只会发送 request/response headers
	// 这里的场景只需要处理 request headers，其他类型的请求目前用不到，先不实现了

	ctx := stream.Context()

	for {

		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		req, err := stream.Recv()
		if err == io.EOF {
			return nil
		}
		if err != nil {
			return status.Errorf(codes.Unknown, "cannot receive stream request: %v", err)
		}

		var resp *extproc.ProcessingResponse

		switch req.Request.(type) {
		case *extproc.ProcessingRequest_RequestHeaders:
			routeName, err := getRouteName(req)
			if err != nil {
				return err
			}
			resp, err = s.buildProcessingResponseForRequestHeaders(routeName)
			if err != nil {
				return err
			}
		case *extproc.ProcessingRequest_ResponseHeaders:
			resp = &extproc.ProcessingResponse{
				Response: &extproc.ProcessingResponse_ResponseHeaders{},
			}
		default:
			resp = &extproc.ProcessingResponse{
				Response: &extproc.ProcessingResponse_ImmediateResponse{
					ImmediateResponse: &extproc.ImmediateResponse{
						Status: &typev3.HttpStatus{
							Code: typev3.StatusCode_BadGateway,
						},
						Body: []byte("Only support processing request/response headers"),
					},
				},
			}
		}

		if err := stream.Send(resp); err != nil {
			log.Printf("发送响应失败: %v", err)
			return err
		}
	}
}

func getRouteName(req *extproc.ProcessingRequest) (string, error) {
	headerMap := make(map[string]string)
	for _, h := range req.GetRequestHeaders().GetHeaders().GetHeaders() {
		headerMap[h.GetKey()] = string(h.GetRawValue())
	}

	// 优先从解析 x-route-name
	if routeName := headerMap[XRouteName]; routeName != "" {
		return routeName, nil
	}

	// 从 host 中解析
	// http2: authority
	// http1: host
	var host string
	if v1 := headerMap[":authority"]; v1 != "" {
		host = v1
	} else if v2 := headerMap["authority"]; v2 != "" {
		host = v2
	} else if v3 := headerMap["host"]; v3 != "" {
		host = v3
	} else {
		return "", status.Errorf(codes.InvalidArgument, "host not found in request")
	}

	// <routeName>-grey-xxx.example.com
	if idx := strings.Index(host, "-grey-"); idx > 0 {
		return host[:idx], nil
	}

	return "", nil
}

func (s *ExtProcServer) buildProcessingResponseForRequestHeaders(routeName string) (*extproc.ProcessingResponse, error) {

	if routeName == "" {
		return &extproc.ProcessingResponse{
			Response: &extproc.ProcessingResponse_RequestHeaders{},
		}, nil
	}

	// use RawValue instead of Value, Value will cause empty value :)
	// see https://github.com/envoyproxy/envoy/issues/31555
	opt := &corev3.HeaderValueOption{
		Header: &corev3.HeaderValue{
			Key:      XRouteName,
			RawValue: []byte(routeName),
		},
		AppendAction: corev3.HeaderValueOption_OVERWRITE_IF_EXISTS_OR_ADD,
	}

	return &extproc.ProcessingResponse{
		Response: &extproc.ProcessingResponse_RequestHeaders{
			RequestHeaders: &extproc.HeadersResponse{
				Response: &extproc.CommonResponse{
					HeaderMutation: &extproc.HeaderMutation{
						SetHeaders: []*corev3.HeaderValueOption{
							opt,
						},
					},
				},
			},
		},
	}, nil
}
