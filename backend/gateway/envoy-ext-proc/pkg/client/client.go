package client

import (
	v1 "github.com/igigdb/igigdb/backend/service-router/service-router-api/src/go/igigdb/service_router_api/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var RouteStub v1.RouteServiceClient

func InitClient() {
	RouteStub = newClient("service-router:8080", v1.NewRouteServiceClient)
}

func newClient[T any](
	authority string, builder func(cc grpc.ClientConnInterface) T,
) T {
	conn, err := grpc.NewClient(
		authority,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		panic(err)
	}
	return builder(conn)
}
