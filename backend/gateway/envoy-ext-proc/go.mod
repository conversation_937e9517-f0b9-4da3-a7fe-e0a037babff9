module github.com/igigdb/igigdb/backend/gateway/envoy-ext-proc

go 1.24.0

replace github.com/igigdb/igigdb/backend/service-router/service-router-api => ../../service-router/service-router-api

require (
	github.com/envoyproxy/go-control-plane/envoy v1.32.4
	google.golang.org/grpc v1.74.2
)

require (
	github.com/cncf/xds/go v0.0.0-20250501225837-2ac532fd4443 // indirect
	github.com/envoyproxy/protoc-gen-validate v1.2.1 // indirect
	github.com/planetscale/vtprotobuf v0.6.1-0.20250313105119-ba97887b0a25 // indirect
	golang.org/x/net v0.42.0 // indirect
	golang.org/x/sys v0.34.0 // indirect
	golang.org/x/text v0.27.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250804133106-a7a43d27e69b // indirect
	google.golang.org/protobuf v1.36.6 // indirect
)
