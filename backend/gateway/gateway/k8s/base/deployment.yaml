apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${SERVICE}
  labels:
    service: ${SERVICE}
    branch: ${BRANCH}
    commit: ${COMMIT}
    datetime: ${DATETIME}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      service: ${SERVICE}
      branch: ${BRANCH}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
        sidecar.istio.io/proxyCPU: "50m"
        sidecar.istio.io/proxyMemory: "64Mi"
      labels:
        service: ${SERVICE}
        branch: ${BRANCH}
        commit: ${COMMIT}
        datetime: ${DATETIME}
    spec:
      containers:
        - name: ${SERVICE}
          image: ${IMAGE}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: 50m
              memory: 128Mi
            limits:
              cpu: 2000m
              memory: 512Mi
          ports:
            - name: http
              containerPort: 8080
            - name: admin
              containerPort: 9901
          startupProbe:
            httpGet:
              path: /ready
              port: 9901
            initialDelaySeconds: 5
            periodSeconds: 2
            timeoutSeconds: 1
            failureThreshold: 30 # 允许较长的启动时间 (2s * 30 = 60s)
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /ready
              port: 9901
            initialDelaySeconds: 5
            periodSeconds: 2
            timeoutSeconds: 1
            failureThreshold: 2
            successThreshold: 1
