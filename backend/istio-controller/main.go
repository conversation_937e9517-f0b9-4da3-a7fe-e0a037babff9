package main

import (
	"github.com/igigdb/igigdb/backend/istio-controller/pkg/controller"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
)

func main() {
	// 优先从环境变量读取配置
	namespaceEnv := os.Getenv("WATCH_NAMESPACES")
	if namespaceEnv == "" {
		namespaceEnv = "testing"
	}

	// 解析要监听的命名空间
	namespaces := strings.Split(namespaceEnv, ",")
	for i, ns := range namespaces {
		namespaces[i] = strings.TrimSpace(ns)
	}

	log.Printf("启动 istio-controller，监听命名空间: %v", namespaces)

	// 创建 Kubernetes 客户端
	k8sClient, istioClient, err := controller.CreateClients()
	if err != nil {
		log.Fatalf("无法创建 Kubernetes 客户端: %v", err)
	}

	// 创建并启动控制器
	ctrl, err := controller.NewController(k8sClient, istioClient, namespaces)
	if err != nil {
		log.Fatalf("无法创建控制器: %v", err)
	}

	go func() {
		if err := ctrl.Start(); err != nil {
			log.Fatalf("控制器启动失败: %v", err)
		}
	}()

	// 记录控制器已启动
	log.Println("控制器已成功启动")

	// 等待终止信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	// 优雅关闭
	log.Println("接收到终止信号，正在关闭...")
	ctrl.Stop()
	log.Println("已关闭")
}
