package controller

import (
	"context"
	"fmt"
	networkingv1alpha3 "istio.io/api/networking/v1alpha3"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	istioclient "istio.io/client-go/pkg/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"log"
	"strings"
)

// IstioConfigGenerator 生成 Istio 配置
type IstioConfigGenerator struct {
	istioClient istioclient.Interface
	cache       *ServiceCache
}

// NewIstioConfigGenerator 创建一个新的 Istio 配置生成器
func NewIstioConfigGenerator(istioClient istioclient.Interface,
	cache *ServiceCache,
) *IstioConfigGenerator {
	return &IstioConfigGenerator{
		istioClient: istioClient,
		cache:       cache,
	}
}

// UpdateIstioConfig 更新服务的 Istio 配置
func (g *IstioConfigGenerator) UpdateIstioConfig(namespace, baseName string, branches []string) error {
	// 创建或更新 DestinationRule
	if err := g.createOrUpdateDestinationRule(namespace, baseName, branches); err != nil {
		return fmt.Errorf("更新 DestinationRule 失败: %w", err)
	}

	// 创建或更新 VirtualService
	if err := g.createOrUpdateVirtualService(namespace, baseName, branches); err != nil {
		return fmt.Errorf("更新 VirtualService 失败: %w", err)
	}

	return nil
}

// createOrUpdateDestinationRule 创建或更新 DestinationRule
func (g *IstioConfigGenerator) createOrUpdateDestinationRule(namespace, baseName string, branches []string) error {
	// 创建子集
	subsets := make([]*networkingv1alpha3.Subset, 0, len(branches))
	for _, branch := range branches {
		subset := &networkingv1alpha3.Subset{
			Name: branch,
			Labels: map[string]string{
				"service": restoreServiceName(baseName, branch),
			},
		}
		subsets = append(subsets, subset)
	}

	// 创建 DestinationRule
	dr := &v1alpha3.DestinationRule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      baseName,
			Namespace: namespace,
		},
		Spec: networkingv1alpha3.DestinationRule{
			Host:    baseName,
			Subsets: subsets,
		},
	}

	// 检查是否已存在
	existing, err := g.istioClient.NetworkingV1alpha3().DestinationRules(namespace).Get(context.TODO(), baseName, metav1.GetOptions{})
	if err == nil {
		// 更新现有资源
		existing.Spec = *dr.Spec.DeepCopy()
		_, err = g.istioClient.NetworkingV1alpha3().DestinationRules(namespace).Update(context.TODO(), existing, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新 DestinationRule %s/%s 失败: %w", namespace, baseName, err)
		}
		log.Printf("已更新 DestinationRule %s/%s", namespace, baseName)
	} else {
		// 创建新资源
		_, err = g.istioClient.NetworkingV1alpha3().DestinationRules(namespace).Create(context.TODO(), dr, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("创建 DestinationRule %s/%s 失败: %w", namespace, baseName, err)
		}
		log.Printf("已创建 DestinationRule %s/%s", namespace, baseName)
	}

	return nil
}

// createOrUpdateVirtualService 创建或更新 VirtualService
func (g *IstioConfigGenerator) createOrUpdateVirtualService(namespace, baseName string, branches []string) error {
	// 创建 HTTP 路由
	httpRoutes := make([]*networkingv1alpha3.HTTPRoute, 0, len(branches))

	// 创建基于 HTTP Header 的路由
	headerName := "x-grey-" + baseName

	// 为每个分支创建路由规则
	for _, branch := range branches {
		service, exist := g.cache.GetService(namespace, restoreServiceName(baseName, branch))
		if !exist {
			log.Printf("服务 %s/%s 不存在，跳过创建路由规则", namespace, service.Name)
			continue
		}
		route := &networkingv1alpha3.HTTPRoute{
			Name: branch,
			Match: []*networkingv1alpha3.HTTPMatchRequest{
				{
					Headers: map[string]*networkingv1alpha3.StringMatch{
						headerName: {
							MatchType: &networkingv1alpha3.StringMatch_Exact{
								Exact: branch,
							},
						},
					},
				},
			},
			Route: []*networkingv1alpha3.HTTPRouteDestination{
				{
					Destination: &networkingv1alpha3.Destination{
						Host:   baseName,
						Subset: branch,
					},
				},
			},
		}
		httpRoutes = append(httpRoutes, route)
	}

	// 添加 branch 不存在返回值
	notFoundRoute := &networkingv1alpha3.HTTPRoute{
		Name: "branch-not-exist",
		Match: []*networkingv1alpha3.HTTPMatchRequest{
			{
				Headers: map[string]*networkingv1alpha3.StringMatch{
					headerName: {
						MatchType: &networkingv1alpha3.StringMatch_Regex{
							Regex: ".*",
						},
					},
				},
			},
		},
		DirectResponse: &networkingv1alpha3.HTTPDirectResponse{
			Status: 404,
			Body: &networkingv1alpha3.HTTPBody{
				Specifier: &networkingv1alpha3.HTTPBody_String_{
					String_: fmt.Sprintf("Invalid branch for service %s, available branches: %s", baseName, strings.Join(branches, ", ")),
				},
			},
		},
	}
	httpRoutes = append(httpRoutes, notFoundRoute)

	// 添加默认路由（main 分支）
	defaultRoute := &networkingv1alpha3.HTTPRoute{
		Name: "default",
		Route: []*networkingv1alpha3.HTTPRouteDestination{
			{
				Destination: &networkingv1alpha3.Destination{
					Host:   baseName,
					Subset: "main",
				},
			},
		},
	}
	httpRoutes = append(httpRoutes, defaultRoute)

	// 创建 VirtualService
	vs := &v1alpha3.VirtualService{
		ObjectMeta: metav1.ObjectMeta{
			Name:      baseName,
			Namespace: namespace,
		},
		Spec: networkingv1alpha3.VirtualService{
			Hosts: []string{baseName},
			Http:  httpRoutes,
		},
	}

	// 检查是否已存在
	existing, err := g.istioClient.NetworkingV1alpha3().VirtualServices(namespace).Get(context.TODO(), baseName, metav1.GetOptions{})
	if err == nil {
		// 更新现有资源
		existing.Spec = *vs.Spec.DeepCopy()
		_, err = g.istioClient.NetworkingV1alpha3().VirtualServices(namespace).Update(context.TODO(), existing, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新 VirtualService %s/%s 失败: %w", namespace, baseName, err)
		}
		log.Printf("已更新 VirtualService %s/%s", namespace, baseName)
	} else {
		// 创建新资源
		_, err = g.istioClient.NetworkingV1alpha3().VirtualServices(namespace).Create(context.TODO(), vs, metav1.CreateOptions{})
		if err != nil {
			return fmt.Errorf("创建 VirtualService %s/%s 失败: %w", namespace, baseName, err)
		}
		log.Printf("已创建 VirtualService %s/%s", namespace, baseName)
	}

	return nil
}

// DeleteIstioResources 删除服务的 Istio 资源
func (g *IstioConfigGenerator) DeleteIstioResources(namespace, name string) error {
	// 提取基础服务名
	baseName, _ := extractServiceInfo(name)
	if baseName == "" {
		return nil
	}

	// 检查是否还有其他相关服务
	// 如果没有，则删除 Istio 资源

	// 删除 VirtualService
	err := g.istioClient.NetworkingV1alpha3().VirtualServices(namespace).Delete(context.TODO(), baseName, metav1.DeleteOptions{})
	if err != nil {
		log.Printf("删除 VirtualService %s/%s 失败: %v", namespace, baseName, err)
	} else {
		log.Printf("已删除 VirtualService %s/%s", namespace, baseName)
	}

	// 删除 DestinationRule
	err = g.istioClient.NetworkingV1alpha3().DestinationRules(namespace).Delete(context.TODO(), baseName, metav1.DeleteOptions{})
	if err != nil {
		log.Printf("删除 DestinationRule %s/%s 失败: %v", namespace, baseName, err)
	} else {
		log.Printf("已删除 DestinationRule %s/%s", namespace, baseName)
	}

	return nil
}

// GetVirtualServices 获取命名空间中的所有 VirtualService
func (g *IstioConfigGenerator) GetVirtualServices(namespace string) ([]*v1alpha3.VirtualService, error) {
	vsList, err := g.istioClient.NetworkingV1alpha3().VirtualServices(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("列出命名空间 %s 中的 VirtualService 失败: %w", namespace, err)
	}
	return vsList.Items, nil
}

// GetDestinationRules 获取命名空间中的所有 DestinationRule
func (g *IstioConfigGenerator) GetDestinationRules(namespace string) ([]*v1alpha3.DestinationRule, error) {
	drList, err := g.istioClient.NetworkingV1alpha3().DestinationRules(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("列出命名空间 %s 中的 DestinationRule 失败: %w", namespace, err)
	}
	return drList.Items, nil
}
