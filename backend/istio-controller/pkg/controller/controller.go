package controller

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	istioclient "istio.io/client-go/pkg/clientset/versioned"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
)

// Controller 监听 Kubernetes Service 变化并更新 Istio 配置
type Controller struct {
	k8sClient   kubernetes.Interface
	istioClient istioclient.Interface
	namespaces  []string
	informers   []cache.SharedIndexInformer
	queue       workqueue.TypedRateLimitingInterface[string]
	generator   *IstioConfigGenerator
	cache       *ServiceCache
	stopCh      chan struct{}
	wg          sync.WaitGroup
}

// NewController 创建一个新的控制器
func NewController(k8sClient kubernetes.Interface, istioClient istioclient.Interface, namespaces []string) (*Controller, error) {
	// 创建服务缓存
	serviceCache := NewServiceCache(k8sClient, namespaces)

	controller := &Controller{
		k8sClient:   k8sClient,
		istioClient: istioClient,
		namespaces:  namespaces,
		queue:       workqueue.NewTypedRateLimitingQueue[string](workqueue.DefaultTypedControllerRateLimiter[string]()),
		generator:   NewIstioConfigGenerator(istioClient, serviceCache),
		cache:       serviceCache,
		stopCh:      make(chan struct{}),
	}

	// 创建多命名空间 informer，但只监听指定的命名空间
	controller.informers = make([]cache.SharedIndexInformer, 0, len(namespaces))

	// 为每个命名空间创建一个 informer
	for _, namespace := range namespaces {
		// 使用 NewSharedInformerFactoryWithOptions 来限制只监听指定的命名空间
		factory := informers.NewSharedInformerFactoryWithOptions(
			k8sClient,
			10*time.Minute,
			informers.WithNamespace(namespace),
		)

		// 创建 service informer
		informer := factory.Core().V1().Services().Informer()

		// 添加事件处理函数
		_, err := informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				service := obj.(*corev1.Service)
				controller.cache.AddOrUpdateService(service)
				controller.enqueueService(obj)
			},
			UpdateFunc: func(old, new interface{}) {
				service := new.(*corev1.Service)
				controller.cache.AddOrUpdateService(service)
				controller.enqueueService(new)
			},
			DeleteFunc: func(obj interface{}) {
				var service *corev1.Service
				var key string
				var err error

				// 处理删除事件时，obj 可能是 DeletedFinalStateUnknown
				if tombstone, ok := obj.(cache.DeletedFinalStateUnknown); ok {
					service, ok = tombstone.Obj.(*corev1.Service)
					if !ok {
						log.Printf("无法解析删除的对象: %v", obj)
						return
					}
				} else {
					service = obj.(*corev1.Service)
				}

				if key, err = cache.MetaNamespaceKeyFunc(service); err != nil {
					log.Printf("无法获取对象键: %v", err)
					return
				}
				controller.cache.DeleteService(service.Namespace, service.Name)
				controller.queue.Add(key)
			},
		})

		if err != nil {
			return nil, err
		}

		// 将 informer 添加到列表中
		controller.informers = append(controller.informers, informer)
	}

	return controller, nil
}

// enqueueService 将服务添加到工作队列
func (c *Controller) enqueueService(obj interface{}) {
	var key string
	var err error
	if key, err = cache.MetaNamespaceKeyFunc(obj); err != nil {
		log.Printf("无法获取对象键: %v", err)
		return
	}
	c.queue.Add(key)
}

// Start 启动控制器
func (c *Controller) Start() error {
	// 首先同步缓存
	log.Println("正在同步服务缓存...")
	if err := c.cache.SyncAll(); err != nil {
		return fmt.Errorf("同步服务缓存失败: %w", err)
	}
	log.Println("服务缓存同步完成")

	// 启动所有 informers
	for _, informer := range c.informers {
		go informer.Run(c.stopCh)
	}

	// 等待所有 informers 的缓存同步
	log.Println("等待 informers 缓存同步...")
	for _, informer := range c.informers {
		if !cache.WaitForCacheSync(c.stopCh, informer.HasSynced) {
			return fmt.Errorf("等待缓存同步超时")
		}
	}
	log.Println("所有 informers 缓存已同步")

	// 启动工作线程
	for i := 0; i < 1; i++ {
		c.wg.Add(1)
		go func() {
			defer c.wg.Done()
			wait.Until(c.runWorker, time.Second, c.stopCh)
		}()
	}

	return nil
}

// runWorker 处理工作队列中的项目
func (c *Controller) runWorker() {
	for c.processNextItem() {
	}
}

// processNextItem 处理队列中的下一个项目
func (c *Controller) processNextItem() bool {
	// 从队列中获取下一个项目
	key, quit := c.queue.Get()
	if quit {
		return false
	}
	defer c.queue.Done(key)

	// 处理项目
	err := c.syncService(key)
	if err != nil {
		log.Printf("处理服务 %s 失败: %v", key, err)
		c.queue.AddRateLimited(key)
		return true
	}

	// 处理成功，重置重试限制
	c.queue.Forget(key)
	return true
}

// syncService 同步单个服务的 Istio 配置
func (c *Controller) syncService(key string) error {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		return fmt.Errorf("无效的键 %s: %w", key, err)
	}

	// 从缓存中获取服务
	service, exists := c.cache.GetService(namespace, name)
	if !exists {
		// 服务可能已被删除
		log.Printf("服务 %s/%s 被删除，删除其 Istio 配置", namespace, name)
		// 尝试删除相关的 Istio 配置
		if err := c.generator.DeleteIstioResources(namespace, name); err != nil {
			log.Printf("删除服务 %s/%s 的 Istio 资源失败: %v", namespace, name, err)
		}
		return nil
	}

	// 提取基础服务名和分支
	baseName, _ := extractServiceInfo(service.Name)
	if baseName == "" {
		// 跳过系统服务或无法解析的服务
		return nil
	}

	// 查找同一基础名称的所有服务
	branches := c.cache.getBranches(namespace, baseName)

	// 更新 Istio 配置
	if err := c.generator.UpdateIstioConfig(namespace, baseName, branches); err != nil {
		return fmt.Errorf("更新 Istio 配置失败: %w", err)
	}

	log.Printf("成功同步服务 %s/%s 的 Istio 配置", namespace, name)
	return nil
}

// Stop 停止控制器
func (c *Controller) Stop() {
	log.Println("正在停止控制器...")
	close(c.stopCh)
	c.queue.ShutDown()
	c.wg.Wait()
	log.Println("控制器已停止")
}

// in: service-app-dev-feature1
// out: service-app, dev-feature1
func extractServiceInfo(serviceName string) (string, string) {
	// 跳过 Kubernetes 系统服务
	if serviceName == "kubernetes" || serviceName == "istio-controller" {
		return "", ""
	}

	// 检查是否是分支服务，以 "-dev-" 为分隔符 (格式: <service>-<branch>)
	if devIndex := strings.Index(serviceName, "-dev-"); devIndex > 0 {
		baseName := serviceName[:devIndex]
		branch := serviceName[devIndex+1:]
		return baseName, branch
	}

	// 主服务
	return serviceName, "main"
}

// in: service-app, dev-feature1
// out: service-app-dev-feature1
func restoreServiceName(baseName, branch string) string {
	if branch == "main" {
		return baseName
	}
	return baseName + "-" + branch
}
