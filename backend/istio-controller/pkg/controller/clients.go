package controller

import (
	"errors"
	"fmt"
	istioclient "istio.io/client-go/pkg/clientset/versioned"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"os"
	"path/filepath"
)

// CreateClients 创建 Kubernetes 和 Istio 客户端
func CreateClients() (kubernetes.Interface, istioclient.Interface, error) {
	var config *rest.Config
	var err error

	// 优先尝试使用集群内配置
	config, err = rest.InClusterConfig()
	if errors.Is(err, rest.ErrNotInCluster) {
		// 使用默认的 kubeconfig 路径
		home := os.Getenv("HOME")
		defaultKubeconfig := filepath.Join(home, ".kube", "config")
		config, err = clientcmd.BuildConfigFromFlags("", defaultKubeconfig)
		if err != nil {
			return nil, nil, fmt.Errorf("无法使用默认的 kubeconfig 文件: %w", err)
		}
	}

	// 创建 Kubernetes 客户端
	k8sClient, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, nil, fmt.Errorf("无法创建 Kubernetes 客户端: %w", err)
	}

	// 创建 Istio 客户端
	istioClient, err := istioclient.NewForConfig(config)
	if err != nil {
		return nil, nil, fmt.Errorf("无法创建 Istio 客户端: %w", err)
	}

	return k8sClient, istioClient, nil
}
