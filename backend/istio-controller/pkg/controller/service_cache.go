package controller

import (
	"context"
	"fmt"
	"log"
	"sync"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
)

// ServiceCache 缓存 Kubernetes Service 信息
type ServiceCache struct {
	k8sClient  kubernetes.Interface
	namespaces []string
	mu         sync.RWMutex
	// 缓存结构: map[namespace]map[serviceName]*corev1.Service
	services map[string]map[string]*corev1.Service
}

// NewServiceCache 创建一个新的服务缓存
func NewServiceCache(k8sClient kubernetes.Interface, namespaces []string) *ServiceCache {
	cache := &ServiceCache{
		k8sClient:  k8sClient,
		namespaces: namespaces,
		services:   make(map[string]map[string]*corev1.Service),
	}

	// 初始化命名空间缓存
	for _, ns := range namespaces {
		cache.services[ns] = make(map[string]*corev1.Service)
	}

	return cache
}

// SyncAll 同步所有命名空间的服务到缓存
func (c *ServiceCache) SyncAll() error {
	for _, ns := range c.namespaces {
		if err := c.syncNamespace(ns); err != nil {
			return fmt.Errorf("同步命名空间 %s 失败: %w", ns, err)
		}
	}
	return nil
}

// syncNamespace 同步指定命名空间的所有服务到缓存
func (c *ServiceCache) syncNamespace(namespace string) error {
	services, err := c.k8sClient.CoreV1().Services(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labels.Everything().String(),
	})
	if err != nil {
		return fmt.Errorf("列出命名空间 %s 中的服务失败: %w", namespace, err)
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 清空当前命名空间的缓存
	c.services[namespace] = make(map[string]*corev1.Service)

	// 添加服务到缓存
	for i := range services.Items {
		svc := &services.Items[i]
		c.services[namespace][svc.Name] = svc
	}

	log.Printf("已同步命名空间 %s 中的 %d 个服务到缓存", namespace, len(services.Items))
	return nil
}

// AddOrUpdateService 添加或更新缓存中的服务
func (c *ServiceCache) AddOrUpdateService(service *corev1.Service) {
	if service == nil {
		return
	}

	namespace := service.Namespace
	if _, exists := c.services[namespace]; !exists {
		// 如果不是我们监听的命名空间，忽略
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()
	c.services[namespace][service.Name] = service
}

// DeleteService 从缓存中删除服务
func (c *ServiceCache) DeleteService(namespace, name string) {
	if _, exists := c.services[namespace]; !exists {
		// 如果不是我们监听的命名空间，忽略
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.services[namespace], name)
}

// GetService 从缓存中获取服务
func (c *ServiceCache) GetService(namespace, name string) (*corev1.Service, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if nsServices, exists := c.services[namespace]; exists {
		if service, found := nsServices[name]; found {
			return service, true
		}
	}
	return nil, false
}

// in: service-app
// out: [main, dev-feature1]
func (c *ServiceCache) getBranches(namespace, baseName string) []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 结果切片: 所有分支名
	result := make([]string, 0)

	if nsServices, exists := c.services[namespace]; exists {
		for serviceName := range nsServices {
			svcBaseName, branch := extractServiceInfo(serviceName)
			if svcBaseName == baseName {
				result = append(result, branch)
			}
		}
	}

	return result
}

// GetServiceBranches 获取指定命名空间中的服务及其分支
func (c *ServiceCache) GetServiceBranches(namespace string) map[string][]string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[string][]string)

	if nsServices, exists := c.services[namespace]; exists {
		for serviceName := range nsServices {
			baseName, branch := extractServiceInfo(serviceName)
			if baseName == "" {
				// 跳过系统服务或无法解析的服务
				continue
			}

			// 添加到结果
			if _, exists := result[baseName]; !exists {
				result[baseName] = make([]string, 0)
			}
			result[baseName] = append(result[baseName], branch)
		}
	}

	return result
}
