package controller

import (
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

func TestServiceCache(t *testing.T) {
	// 创建假的 Kubernetes 客户端
	k8sClient := fake.NewClientset()

	// 创建测试命名空间
	namespace := "testing"

	// 创建服务缓存
	cache := NewServiceCache(k8sClient, []string{namespace})

	// 测试添加服务
	service1 := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "service-app",
			Namespace: namespace,
		},
	}
	service2 := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "service-app-dev-feature1",
			Namespace: namespace,
		},
	}

	// 添加服务到缓存
	cache.AddOrUpdateService(service1)
	cache.AddOrUpdateService(service2)

	// 测试获取服务
	svc, exists := cache.GetService(namespace, "service-app")
	if !exists {
		t.Errorf("服务 service-app 应该存在于缓存中")
	}
	if svc.Name != "service-app" {
		t.Errorf("服务名称不匹配，期望 service-app，实际 %s", svc.Name)
	}

	// 测试查找相关服务
	relatedBranches := cache.getBranches(namespace, "service-app")
	if len(relatedBranches) != 2 {
		t.Errorf("应该找到 2 个相关分支，实际找到 %d", len(relatedBranches))
	}

	// 检查是否包含预期的分支
	mainFound := false
	feature1Found := false
	for _, branch := range relatedBranches {
		if branch == "main" {
			mainFound = true
		}
		if branch == "dev-feature1" {
			feature1Found = true
		}
	}

	if !mainFound {
		t.Errorf("应该找到 main 分支")
	}
	if !feature1Found {
		t.Errorf("应该找到 dev-feature1 分支")
	}

	// 测试获取服务分支
	branches := cache.GetServiceBranches(namespace)
	if len(branches) != 1 {
		t.Errorf("应该找到 1 个基础服务，实际找到 %d", len(branches))
	}
	if len(branches["service-app"]) != 2 {
		t.Errorf("service-app 应该有 2 个分支，实际有 %d", len(branches["service-app"]))
	}

	// 测试删除服务
	cache.DeleteService(namespace, "service-app")
	_, exists = cache.GetService(namespace, "service-app")
	if exists {
		t.Errorf("服务 service-app 应该已从缓存中删除")
	}
}

func TestExtractServiceInfo(t *testing.T) {
	testCases := []struct {
		name           string
		serviceName    string
		expectedBase   string
		expectedBranch string
	}{
		{
			name:           "主服务",
			serviceName:    "service-app",
			expectedBase:   "service-app",
			expectedBranch: "main",
		},
		{
			name:           "分支服务",
			serviceName:    "service-app-dev-feature1",
			expectedBase:   "service-app",
			expectedBranch: "dev-feature1",
		},
		{
			name:           "分支服务名称包含多个 -dev-，取第一个",
			serviceName:    "service-app-dev-feature1-dev-feature2",
			expectedBase:   "service-app",
			expectedBranch: "dev-feature1-dev-feature2",
		},
		{
			name:           "分支服务名 -dev- 之后没有值",
			serviceName:    "service-app-dev-",
			expectedBase:   "service-app",
			expectedBranch: "dev-",
		},
		{
			name:           "系统服务",
			serviceName:    "kubernetes",
			expectedBase:   "",
			expectedBranch: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			base, branch := extractServiceInfo(tc.serviceName)
			if base != tc.expectedBase {
				t.Errorf("基础名称不匹配，期望 %s，实际 %s", tc.expectedBase, base)
			}
			if branch != tc.expectedBranch {
				t.Errorf("分支名称不匹配，期望 %s，实际 %s", tc.expectedBranch, branch)
			}
		})
	}
}
