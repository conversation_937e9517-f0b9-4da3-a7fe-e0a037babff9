apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${SERVICE}
  labels:
    service: ${SERVICE}
    branch: ${BRANCH}
    commit: ${COMMIT}
    datetime: ${DATETIME}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      service: ${SERVICE}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        service: ${SERVICE}
        branch: ${BRANCH}
        commit: ${COMMIT}
        datetime: ${DATETIME}
    spec:
      serviceAccountName: ${SERVICE}
      containers:
        - name: ${SERVICE}
          image: ${IMAGE}
          imagePullPolicy: Always
