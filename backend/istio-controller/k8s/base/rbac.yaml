apiVersion: v1
kind: ServiceAccount
metadata:
  name: ${SERVICE}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ${SERVICE}
rules:
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["*"]
  - apiGroups: ["networking.istio.io"]
    resources: ["virtualservices", "destinationrules"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ${SERVICE}
subjects:
  - kind: ServiceAccount
    name: ${SERVICE}
    namespace: ${NAMESPACE}
roleRef:
  kind: Role
  name: ${SERVICE}
  apiGroup: rbac.authorization.k8s.io
