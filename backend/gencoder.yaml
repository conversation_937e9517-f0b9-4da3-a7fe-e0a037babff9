# 使用 https://github.com/DanielLiu1123/gencoder 解析数据库表结构生成模版代码，比如 model 等等。
# 1. 安装：go install github.com/DanielLiu1123/gencoder/cmd/gencoder@main
# 2. 配置：修改 database[].tables[] 为需要生成的表名
# 3. 生成：gencoder generate: cd $(git rev-parse --show-toplevel)/backend && gencoder generate

templates: templates/gencoder
helpers:
  - templates/gencoder/helper.js
properties:
  baseDir: "."
databases:
  - dsn: "postgres://avnadmin:<EMAIL>:10780/testing?sslmode=require"
    properties:
      protoPackage: "igigdb.router.v1"
    tables:
      - name: "route"
        properties:
          baseDir: "proto"
