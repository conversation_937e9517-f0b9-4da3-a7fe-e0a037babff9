syntax = "proto3";

package igigdb.app.v1;

option go_package = "igigdb/app/v1;app";
option java_multiple_files = true;

service EventService {
  rpc CreateEvent(CreateEventRequest) returns (CreateEventResponse);
}

message CreateEventRequest {
  string title = 1;
  string description = 2;
  string start_time = 3;
  string end_time = 4;
  string location = 5;
  string image_url = 6;
}

message CreateEventResponse {
  int64 id = 1;
}
