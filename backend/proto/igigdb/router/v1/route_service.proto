// @gencoder.generated: service-router/service-router-api/api/igigdb/router/v1/route_service.proto

syntax = "proto3";

package igigdb.router.v1;

import "igigdb/router/v1/route.proto";

option go_package = "igigdb/router/v1;router";
option java_multiple_files = true;
option java_package = "igigdb.router.v1";

service RouteService {
  rpc CreateRoute(CreateRouteRequest) returns (CreateRouteResponse);
  rpc UpdateRoute(UpdateRouteRequest) returns (UpdateRouteResponse);
  rpc DeleteRoute(DeleteRouteRequest) returns (DeleteRouteResponse);
}

// CreateRoute request
message CreateRouteRequest {
  // uniq route name
  optional string name = 2;
  // route description
  optional string description = 3;
  // service configs
  repeated RouteModel.ServiceConfig service_configs = 4;
  // created at
  optional int64 created_at = 5;
  // updated at
  optional int64 updated_at = 6;
  // deleted at
  optional int64 deleted_at = 7;
}

// CreateRoute response
message CreateRouteResponse {
  // created Route id
  int64 id = 1;
}

// UpdateRoute request
message UpdateRouteRequest {
  // id
  int64 id = 1;
  // uniq route name
  optional string name = 2;
  // route description
  optional string description = 3;
  // service configs
  repeated RouteModel.ServiceConfig service_configs = 4;
  // created at
  optional int64 created_at = 5;
  // updated at
  optional int64 updated_at = 6;
  // deleted at
  optional int64 deleted_at = 7;
}

// UpdateRoute response
message UpdateRouteResponse {
  // update success?
  bool success = 1;
}

// DeleteRoute request
message DeleteRouteRequest {
  // id
  int64 id = 1;
}

// DeleteRoute response
message DeleteRouteResponse {
  // delete success?
  bool success = 1;
}
