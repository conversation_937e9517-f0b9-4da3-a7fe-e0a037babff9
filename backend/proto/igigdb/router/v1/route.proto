// @gencoder.generated: service-router/service-router-api/api/igigdb/router/v1/route.proto

syntax = "proto3";

package igigdb.router.v1;

option go_package = "igigdb/router/v1;router";
option java_multiple_files = true;
option java_package = "igigdb.router.v1";

// table: route
// comment:
// indexes:
//   route_pk: (id)
//   route_name_uindex: (name)
message RouteModel {
  // id
  int64 id = 1;
  // uniq route name
  string name = 2;
  // route description
  string description = 3;
  // service configs
  repeated ServiceConfig service_configs = 4;
  // created at
  int64 created_at = 5;
  // updated at
  int64 updated_at = 6;
  // deleted at
  optional int64 deleted_at = 7;

  message ServiceConfig {
    string service = 1;
    string branch = 2;
    int32 weight = 3;
  }
}
