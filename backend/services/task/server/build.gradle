plugins{
    id "org.springframework.boot"
}

dependencies {
    implementation("org.springframework.boot:spring-boot-starter")

    // lib
    implementation(project(":lib-java:core"))

    // api
    implementation(project(":proto:igigdb:crawler"))

    // grpc client
    implementation("io.github.danielliu1123:grpc-client-boot-starter")

    // test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

bootJar {
    archiveBaseName = "app"
}
