plugins{
    id "org.springframework.boot"
}

dependencies {
    // lib
    implementation(project(":lib-java:core"))
    implementation(project(":lib-java:mybatis"))

    // api
    implementation(project(":proto:igigdb:crawler"))

    // grpc
    implementation("io.github.danielliu1123:grpc-server-boot-starter")

    // db
    implementation("org.postgresql:postgresql")

    // mybatis
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")

    // selenium for web scraping (replaced with playwright)
    // implementation("org.seleniumhq.selenium:selenium-java:${seleniumJavaVersion}")

    // playwright for web scraping
    implementation("com.microsoft.playwright:playwright:${playwrightVersion}")

    // test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

bootJar {
    archiveBaseName = "app"
}

apply from: "${rootDir}/gradle/mybatis-generator.gradle"
