# Crawler Server

## Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/crawler/server
gradle bootRun
```

## Gen

```bash
cd $(git rev-parse --show-toplevel)/backend/services/router/server
gradle mbGenerator
```

## Docker

### Build

```bash
cd $(git rev-parse --show-toplevel)/backend/services/crawler/server

docker build -t crawler .
```

### Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/crawler/server
docker run -p 8080:8080 crawler
```
