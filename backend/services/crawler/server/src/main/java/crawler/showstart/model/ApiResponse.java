package crawler.showstart.model;

/**
 * Base response model for Showstart API.
 *
 * @param <T> The type of the result data
 * <AUTHOR>
 * @since 2025/4/27
 */
public record ApiResponse<T>(Integer status, String state, T result, String traceId) {
    /**
     * Checks if the API call was successful.
     *
     * @return true if the status is 200 and state is "1", false otherwise
     */
    public boolean isSuccess() {
        return status != null && status == 200 && "1".equals(state);
    }
}
