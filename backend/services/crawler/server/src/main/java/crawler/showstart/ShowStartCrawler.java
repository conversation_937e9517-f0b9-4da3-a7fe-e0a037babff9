package crawler.showstart;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitUntilState;
import igigdb.lib.core.profile.Profile;
import igigdb.lib.core.util.JsonUtil;
import igigdb.lib.core.util.SpringUtil;
import crawler.showstart.model.ActivityPageResult;
import crawler.showstart.model.ApiResponse;
import crawler.entity.ShowStartActivity;
import crawler.entity.ShowStartActivityParams;
import crawler.entity.ShowStartCity;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

/**
 * 使用无头浏览器爬取秀动网站数据
 */
@Component
public class ShowStartCrawler {
    private static final Logger log = LoggerFactory.getLogger(ShowStartCrawler.class);

    private static final String BASE_URL = "https://www.showstart.com";

    /**
     * 获取Playwright浏览器实例
     *
     * @param playwright Playwright实例
     * @return Playwright浏览器实例
     */
    private static Browser getBrowser(Playwright playwright) {
        return playwright.chromium().launch();
    }

    /**
     * 创建浏览器上下文
     *
     * @param browser 浏览器实例
     * @return 浏览器上下文
     */
    private static BrowserContext createBrowserContext(Browser browser) {
        return browser.newContext();
    }

    /**
     * 爬取所有 activity params
     *
     * @return activity params 列表
     */
    public List<ShowStartActivityParams> crawlActivityParams() {
        try (var playwright = getPlaywright();
                var browser = getBrowser(playwright)) {
            var context = createBrowserContext(browser);
            return doCrawlActivityParams(context.newPage());
        }
    }

    private List<ShowStartActivityParams> doCrawlActivityParams(Page page) {
        var latch = new CountDownLatch(1);
        var activityParamsResponse = new AtomicReference<String>();

        // 监听请求完成事件
        page.onResponse(response -> {
            String url = response.url();
            if (url.endsWith("/api/web/activity/list/params")) {
                try {
                    String body = response.text();
                    activityParamsResponse.set(body);
                    latch.countDown();
                } catch (Exception e) {
                    log.error("获取响应体失败", e);
                }
            }
        });

        // 加载页面，city code 10 是北京
        String url = BASE_URL + "/event/list?pageNo=1&pageSize=20&cityCode=10";
        page.navigate(url);

        // 等待页面加载完成
        page.waitForLoadState(LoadState.DOMCONTENTLOADED);

        // 点击"最近一周内"，这个操作会真实触发请求
        sleep(200);
        page.click(
                "#__layout > section > main > div > div.filter-box > div.select-tag-time.select-tag.clearfix > div > span:nth-child(3)");

        try {
            if (!latch.await(30, TimeUnit.SECONDS)) {
                log.error("等待 activity params 信息响应超时（{}秒）", 30);
                return Collections.emptyList();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }

        ApiResponse<List<ShowStartActivityParams>> response =
                JsonUtil.toBean(activityParamsResponse.get(), new ParameterizedTypeReference<>() {});

        log.info("成功爬取 {} 个 activity params 数据", response.result().size());

        return response.result();
    }

    /**
     * 爬取所有城市信息
     *
     * @return 城市列表
     */
    public List<ShowStartCity> crawlCities() {
        try (var playwright = getPlaywright();
                var browser = getBrowser(playwright)) {
            var context = createBrowserContext(browser);
            return doCrawlCities(context.newPage());
        }
    }

    private static Playwright getPlaywright() {
        var additionalProps = new HashMap<String, String>();
        if (SpringUtil.getContext().getBean(Profile.class) == Profile.LOCAL) {
            // https://playwright.dev/java/docs/debug#run-in-debug-mode
            //                        additionalProps.put("PWDEBUG", "1");
        }
        return Playwright.create(new Playwright.CreateOptions().setEnv(additionalProps));
    }

    private List<ShowStartCity> doCrawlCities(Page page) {

        log.info("开始爬取城市数据...");

        var latch = new CountDownLatch(1);
        var cityResponse = new AtomicReference<List<ShowStartCity>>();

        // 监听请求完成事件
        page.onResponse(response -> {
            String url = response.url();
            if (url.endsWith("/api/web/city/allCity")) {
                try {
                    String body = response.text();
                    var allCityResp = JsonUtil.toBean(
                            body, new ParameterizedTypeReference<ApiResponse<List<ShowStartCity>>>() {});
                    if (allCityResp.result() != null) {
                        cityResponse.set(allCityResp.result());
                        latch.countDown();
                    }
                } catch (Exception e) {
                    log.error("获取响应体失败", e);
                }
            }
        });

        // 加载页面，city code 10 是北京
        String url = BASE_URL + "/event/list?pageNo=1&pageSize=20&cityCode=10&showTime=1";
        page.navigate(url, new Page.NavigateOptions().setWaitUntil(WaitUntilState.LOAD));

        try {
            if (!latch.await(30, TimeUnit.SECONDS)) {
                log.error("等待 city 信息响应超时（{}秒）", 30);
                return List.of();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }

        var result = cityResponse.get().stream()
                .filter(e -> !Objects.equals(e.getName(), "待定")) // 过滤"待定"城市
                .toList();

        log.info("成功爬取 {} 个城市数据", result.size());

        return result;
    }

    /**
     * 爬取最近一周内的活动列表，自动获取所有页的数据
     *
     * @param cityCode 城市代码
     * @return 所有活动列表
     */
    public List<ShowStartActivity> crawlActivities(String cityCode) {
        try (var playwright = getPlaywright();
                var browser = getBrowser(playwright)) {
            var context = createBrowserContext(browser);
            return doCrawlActivities(context.newPage(), cityCode);
        }
    }

    /**
     * 爬取所有页的活动数据
     *
     * @param page     Playwright页面对象
     * @param cityCode 城市代码
     * @return 所有活动列表
     */
    private List<ShowStartActivity> doCrawlActivities(Page page, String cityCode) {
        var allActivities = new LinkedHashSet<ShowStartActivity>();
        int maxPages = 50; // 最大爬取页数，防止无限循环

        // 加载页面，使用指定的城市代码，演出时间选择"全部"
        String url = BASE_URL + "/event/list?pageNo=1&pageSize=20&cityCode=" + cityCode;
        page.navigate(url, new Page.NavigateOptions().setWaitUntil(WaitUntilState.LOAD));

        var recentWeekButton = page.getByText("最近一周内");

        // 点击"最近一周内"
        var firstPageResponse = page.waitForResponse(
                response -> response.url().endsWith("/api/web/activity/list"), recentWeekButton::click);

        // 解析第一页响应
        var firstPageResult = JsonUtil.toBean(
                        firstPageResponse.text(), new ParameterizedTypeReference<ApiResponse<ActivityPageResult>>() {})
                .result();
        var firstPageActivities = firstPageResult.result();
        log.info("成功爬取第 1 页活动数据，共 {} 条记录", firstPageActivities.size());

        // 添加第一页数据到结果列表
        allActivities.addAll(firstPageActivities);

        // 如果有多页，继续爬取
        int totalPages = Math.min(firstPageResult.totalPage(), maxPages);
        if (firstPageResult.totalPage() > maxPages) {
            log.warn("活动列表为 {} 页，只爬取前 {} 页", firstPageResult.totalPage(), maxPages);
        }

        for (int pageNo = 2; pageNo <= totalPages; pageNo++) {

            // sleep 1s，防止被反爬
            sleep(1000);

            // 点击下一页按钮
            var currentPageResponse = page.waitForResponse(
                    response -> response.url().endsWith("/api/web/activity/list"), () -> page.locator(
                                    "#__layout > section > main > div > div.el-pagination.is-background > button.btn-next")
                            .click());

            var currentPageResult = JsonUtil.toBean(
                            currentPageResponse.text(),
                            new ParameterizedTypeReference<ApiResponse<ActivityPageResult>>() {})
                    .result();
            var currentPageActivities = currentPageResult.result();
            if (currentPageActivities == null) {
                log.warn("currentPageActivities is null, currentPageResponse: {}", currentPageResponse.text());
                continue;
            }

            log.info("成功爬取第 {} 页活动数据，共 {} 条记录", pageNo, currentPageActivities.size());

            // 添加到结果列表
            allActivities.addAll(currentPageActivities);

            // 如果没有下一页了，结束爬取
            if (!currentPageResult.hasNextPage()) {
                break;
            }
        }

        log.info("成功爬取 city '{}' 所有活动数据，共 {} 条记录", cityCode, allActivities.size());
        return List.copyOf(allActivities);
    }

    private static void sleep(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
