package crawler.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ShowStartActivityDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    public static final ShowStartActivity showStartActivity = new ShowStartActivity();

    /**
     * Database Column Remarks:
     *   活动ID - 唯一标识符，来自Showstart的原始ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.id")
    public static final SqlColumn<Long> id = showStartActivity.id;

    /**
     * Database Column Remarks:
     *   活动标题 - 演出活动的完整名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.title")
    public static final SqlColumn<String> title = showStartActivity.title;

    /**
     * Database Column Remarks:
     *   海报图片URL - 活动海报图片的完整URL地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.poster")
    public static final SqlColumn<String> poster = showStartActivity.poster;

    /**
     * Database Column Remarks:
     *   是否独家 - 标识活动是否为独家演出（2表示否，1表示是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_exclusive")
    public static final SqlColumn<Integer> isExclusive = showStartActivity.isExclusive;

    /**
     * Database Column Remarks:
     *   表演者/艺术家 - 参与演出的艺术家或表演者名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.performers")
    public static final SqlColumn<String> performers = showStartActivity.performers;

    /**
     * Database Column Remarks:
     *   价格 - 演出票价信息，通常包含货币符号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.price")
    public static final SqlColumn<String> price = showStartActivity.price;

    /**
     * Database Column Remarks:
     *   演出时间 - 活动开始的具体日期和时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.show_time")
    public static final SqlColumn<String> showTime = showStartActivity.showTime;

    /**
     * Database Column Remarks:
     *   演出场地名称 - 活动举办的具体场馆名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.site_name")
    public static final SqlColumn<String> siteName = showStartActivity.siteName;

    /**
     * Database Column Remarks:
     *   城市名称 - 活动举办的城市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.city_name")
    public static final SqlColumn<String> cityName = showStartActivity.cityName;

    /**
     * Database Column Remarks:
     *   是否团购 - 标识活动是否支持团购（1表示是，0表示否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_group")
    public static final SqlColumn<Integer> isGroup = showStartActivity.isGroup;

    /**
     * Database Column Remarks:
     *   是否售罄 - 标识活动票是否已售罄（0表示售罄，1表示未售罄）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.sold_out")
    public static final SqlColumn<Integer> soldOut = showStartActivity.soldOut;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    public static final class ShowStartActivity extends AliasableSqlTable<ShowStartActivity> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> title = column("title", JDBCType.VARCHAR);

        public final SqlColumn<String> poster = column("poster", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isExclusive = column("is_exclusive", JDBCType.INTEGER);

        public final SqlColumn<String> performers = column("performers", JDBCType.VARCHAR);

        public final SqlColumn<String> price = column("price", JDBCType.VARCHAR);

        public final SqlColumn<String> showTime = column("show_time", JDBCType.VARCHAR);

        public final SqlColumn<String> siteName = column("site_name", JDBCType.VARCHAR);

        public final SqlColumn<String> cityName = column("city_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isGroup = column("is_group", JDBCType.INTEGER);

        public final SqlColumn<Integer> soldOut = column("sold_out", JDBCType.INTEGER);

        public ShowStartActivity() {
            super("show_start_activity", ShowStartActivity::new);
        }
    }
}