package crawler.showstart.model;

import crawler.entity.ShowStartActivity;
import java.util.List;

/**
 * Represents a paginated result of activities in Showstart.
 *
 * <AUTHOR>
 * @since 2025/4/27
 */
public record ActivityPageResult(
        Integer pageNo, Integer pageSize, List<ShowStartActivity> result, Integer totalCount, Integer totalPage) {

    public boolean hasNextPage() {
        return pageNo != null && totalPage != null && pageNo < totalPage;
    }
}
