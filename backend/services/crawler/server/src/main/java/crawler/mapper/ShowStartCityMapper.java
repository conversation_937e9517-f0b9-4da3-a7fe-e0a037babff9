package crawler.mapper;

import static crawler.mapper.ShowStartCityDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import crawler.entity.ShowStartCity;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ShowStartCityMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonInsertMapper<ShowStartCity>, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    BasicColumn[] selectList = BasicColumn.columnList(id, countryCode, provinceCode, code, baiduCode, administrativeCode, name, nameEn, en, homeDisplay, homeDisplayEnum, nearbyCity, isHot, hotSort, longitude, latitude, photo, otwCode);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ShowStartCityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="country_code", property="countryCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="province_code", property="provinceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="code", property="code", jdbcType=JdbcType.VARCHAR),
        @Result(column="baidu_code", property="baiduCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="administrative_code", property="administrativeCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="name_en", property="nameEn", jdbcType=JdbcType.VARCHAR),
        @Result(column="en", property="en", jdbcType=JdbcType.VARCHAR),
        @Result(column="home_display", property="homeDisplay", jdbcType=JdbcType.INTEGER),
        @Result(column="home_display_enum", property="homeDisplayEnum", jdbcType=JdbcType.VARCHAR),
        @Result(column="nearby_city", property="nearbyCity", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_hot", property="isHot", jdbcType=JdbcType.INTEGER),
        @Result(column="hot_sort", property="hotSort", jdbcType=JdbcType.INTEGER),
        @Result(column="longitude", property="longitude", jdbcType=JdbcType.DOUBLE),
        @Result(column="latitude", property="latitude", jdbcType=JdbcType.DOUBLE),
        @Result(column="photo", property="photo", jdbcType=JdbcType.VARCHAR),
        @Result(column="otw_code", property="otwCode", jdbcType=JdbcType.VARCHAR)
    })
    List<ShowStartCity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ShowStartCityResult")
    Optional<ShowStartCity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int insertMultiple(Collection<ShowStartCity> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, showStartCity, c ->
            c.map(id).toProperty("id")
            .map(countryCode).toProperty("countryCode")
            .map(provinceCode).toProperty("provinceCode")
            .map(code).toProperty("code")
            .map(baiduCode).toProperty("baiduCode")
            .map(administrativeCode).toProperty("administrativeCode")
            .map(name).toProperty("name")
            .map(nameEn).toProperty("nameEn")
            .map(en).toProperty("en")
            .map(homeDisplay).toProperty("homeDisplay")
            .map(homeDisplayEnum).toProperty("homeDisplayEnum")
            .map(nearbyCity).toProperty("nearbyCity")
            .map(isHot).toProperty("isHot")
            .map(hotSort).toProperty("hotSort")
            .map(longitude).toProperty("longitude")
            .map(latitude).toProperty("latitude")
            .map(photo).toProperty("photo")
            .map(otwCode).toProperty("otwCode")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int insertSelective(ShowStartCity row) {
        return MyBatis3Utils.insert(this::insert, row, showStartCity, c ->
            c.map(id).toPropertyWhenPresent("id", row::getId)
            .map(countryCode).toPropertyWhenPresent("countryCode", row::getCountryCode)
            .map(provinceCode).toPropertyWhenPresent("provinceCode", row::getProvinceCode)
            .map(code).toPropertyWhenPresent("code", row::getCode)
            .map(baiduCode).toPropertyWhenPresent("baiduCode", row::getBaiduCode)
            .map(administrativeCode).toPropertyWhenPresent("administrativeCode", row::getAdministrativeCode)
            .map(name).toPropertyWhenPresent("name", row::getName)
            .map(nameEn).toPropertyWhenPresent("nameEn", row::getNameEn)
            .map(en).toPropertyWhenPresent("en", row::getEn)
            .map(homeDisplay).toPropertyWhenPresent("homeDisplay", row::getHomeDisplay)
            .map(homeDisplayEnum).toPropertyWhenPresent("homeDisplayEnum", row::getHomeDisplayEnum)
            .map(nearbyCity).toPropertyWhenPresent("nearbyCity", row::getNearbyCity)
            .map(isHot).toPropertyWhenPresent("isHot", row::getIsHot)
            .map(hotSort).toPropertyWhenPresent("hotSort", row::getHotSort)
            .map(longitude).toPropertyWhenPresent("longitude", row::getLongitude)
            .map(latitude).toPropertyWhenPresent("latitude", row::getLatitude)
            .map(photo).toPropertyWhenPresent("photo", row::getPhoto)
            .map(otwCode).toPropertyWhenPresent("otwCode", row::getOtwCode)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default Optional<ShowStartCity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default List<ShowStartCity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default List<ShowStartCity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default Optional<ShowStartCity> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, showStartCity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    static UpdateDSL<UpdateModel> updateAllColumns(ShowStartCity row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(countryCode).equalTo(row::getCountryCode)
                .set(provinceCode).equalTo(row::getProvinceCode)
                .set(code).equalTo(row::getCode)
                .set(baiduCode).equalTo(row::getBaiduCode)
                .set(administrativeCode).equalTo(row::getAdministrativeCode)
                .set(name).equalTo(row::getName)
                .set(nameEn).equalTo(row::getNameEn)
                .set(en).equalTo(row::getEn)
                .set(homeDisplay).equalTo(row::getHomeDisplay)
                .set(homeDisplayEnum).equalTo(row::getHomeDisplayEnum)
                .set(nearbyCity).equalTo(row::getNearbyCity)
                .set(isHot).equalTo(row::getIsHot)
                .set(hotSort).equalTo(row::getHotSort)
                .set(longitude).equalTo(row::getLongitude)
                .set(latitude).equalTo(row::getLatitude)
                .set(photo).equalTo(row::getPhoto)
                .set(otwCode).equalTo(row::getOtwCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ShowStartCity row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(countryCode).equalToWhenPresent(row::getCountryCode)
                .set(provinceCode).equalToWhenPresent(row::getProvinceCode)
                .set(code).equalToWhenPresent(row::getCode)
                .set(baiduCode).equalToWhenPresent(row::getBaiduCode)
                .set(administrativeCode).equalToWhenPresent(row::getAdministrativeCode)
                .set(name).equalToWhenPresent(row::getName)
                .set(nameEn).equalToWhenPresent(row::getNameEn)
                .set(en).equalToWhenPresent(row::getEn)
                .set(homeDisplay).equalToWhenPresent(row::getHomeDisplay)
                .set(homeDisplayEnum).equalToWhenPresent(row::getHomeDisplayEnum)
                .set(nearbyCity).equalToWhenPresent(row::getNearbyCity)
                .set(isHot).equalToWhenPresent(row::getIsHot)
                .set(hotSort).equalToWhenPresent(row::getHotSort)
                .set(longitude).equalToWhenPresent(row::getLongitude)
                .set(latitude).equalToWhenPresent(row::getLatitude)
                .set(photo).equalToWhenPresent(row::getPhoto)
                .set(otwCode).equalToWhenPresent(row::getOtwCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    default int updateByPrimaryKeySelective(ShowStartCity row) {
        return update(c ->
            c.set(countryCode).equalToWhenPresent(row::getCountryCode)
            .set(provinceCode).equalToWhenPresent(row::getProvinceCode)
            .set(code).equalToWhenPresent(row::getCode)
            .set(baiduCode).equalToWhenPresent(row::getBaiduCode)
            .set(administrativeCode).equalToWhenPresent(row::getAdministrativeCode)
            .set(name).equalToWhenPresent(row::getName)
            .set(nameEn).equalToWhenPresent(row::getNameEn)
            .set(en).equalToWhenPresent(row::getEn)
            .set(homeDisplay).equalToWhenPresent(row::getHomeDisplay)
            .set(homeDisplayEnum).equalToWhenPresent(row::getHomeDisplayEnum)
            .set(nearbyCity).equalToWhenPresent(row::getNearbyCity)
            .set(isHot).equalToWhenPresent(row::getIsHot)
            .set(hotSort).equalToWhenPresent(row::getHotSort)
            .set(longitude).equalToWhenPresent(row::getLongitude)
            .set(latitude).equalToWhenPresent(row::getLatitude)
            .set(photo).equalToWhenPresent(row::getPhoto)
            .set(otwCode).equalToWhenPresent(row::getOtwCode)
            .where(id, isEqualTo(row::getId))
        );
    }
}