package crawler.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ShowStartCityDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    public static final ShowStartCity showStartCity = new ShowStartCity();

    /**
     * Database Column Remarks:
     *   城市ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.id")
    public static final SqlColumn<Long> id = showStartCity.id;

    /**
     * Database Column Remarks:
     *   国家代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.country_code")
    public static final SqlColumn<String> countryCode = showStartCity.countryCode;

    /**
     * Database Column Remarks:
     *   省份代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.province_code")
    public static final SqlColumn<String> provinceCode = showStartCity.provinceCode;

    /**
     * Database Column Remarks:
     *   城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.code")
    public static final SqlColumn<String> code = showStartCity.code;

    /**
     * Database Column Remarks:
     *   百度地图城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.baidu_code")
    public static final SqlColumn<String> baiduCode = showStartCity.baiduCode;

    /**
     * Database Column Remarks:
     *   行政区划代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.administrative_code")
    public static final SqlColumn<String> administrativeCode = showStartCity.administrativeCode;

    /**
     * Database Column Remarks:
     *   城市名称（中文）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name")
    public static final SqlColumn<String> name = showStartCity.name;

    /**
     * Database Column Remarks:
     *   城市名称（英文大写）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name_en")
    public static final SqlColumn<String> nameEn = showStartCity.nameEn;

    /**
     * Database Column Remarks:
     *   城市名称（英文）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.en")
    public static final SqlColumn<String> en = showStartCity.en;

    /**
     * Database Column Remarks:
     *   是否在首页显示（1表示显示，0表示不显示）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display")
    public static final SqlColumn<Integer> homeDisplay = showStartCity.homeDisplay;

    /**
     * Database Column Remarks:
     *   首页显示枚举值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display_enum")
    public static final SqlColumn<String> homeDisplayEnum = showStartCity.homeDisplayEnum;

    /**
     * Database Column Remarks:
     *   附近城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.nearby_city")
    public static final SqlColumn<String> nearbyCity = showStartCity.nearbyCity;

    /**
     * Database Column Remarks:
     *   是否热门城市（1表示是，0表示否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.is_hot")
    public static final SqlColumn<Integer> isHot = showStartCity.isHot;

    /**
     * Database Column Remarks:
     *   热门城市排序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.hot_sort")
    public static final SqlColumn<Integer> hotSort = showStartCity.hotSort;

    /**
     * Database Column Remarks:
     *   经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.longitude")
    public static final SqlColumn<Double> longitude = showStartCity.longitude;

    /**
     * Database Column Remarks:
     *   纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.latitude")
    public static final SqlColumn<Double> latitude = showStartCity.latitude;

    /**
     * Database Column Remarks:
     *   城市图片URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.photo")
    public static final SqlColumn<String> photo = showStartCity.photo;

    /**
     * Database Column Remarks:
     *   OTW代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.otw_code")
    public static final SqlColumn<String> otwCode = showStartCity.otwCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    public static final class ShowStartCity extends AliasableSqlTable<ShowStartCity> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> countryCode = column("country_code", JDBCType.VARCHAR);

        public final SqlColumn<String> provinceCode = column("province_code", JDBCType.VARCHAR);

        public final SqlColumn<String> code = column("code", JDBCType.VARCHAR);

        public final SqlColumn<String> baiduCode = column("baidu_code", JDBCType.VARCHAR);

        public final SqlColumn<String> administrativeCode = column("administrative_code", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("\"name\"", JDBCType.VARCHAR);

        public final SqlColumn<String> nameEn = column("name_en", JDBCType.VARCHAR);

        public final SqlColumn<String> en = column("en", JDBCType.VARCHAR);

        public final SqlColumn<Integer> homeDisplay = column("home_display", JDBCType.INTEGER);

        public final SqlColumn<String> homeDisplayEnum = column("home_display_enum", JDBCType.VARCHAR);

        public final SqlColumn<String> nearbyCity = column("nearby_city", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isHot = column("is_hot", JDBCType.INTEGER);

        public final SqlColumn<Integer> hotSort = column("hot_sort", JDBCType.INTEGER);

        public final SqlColumn<Double> longitude = column("longitude", JDBCType.DOUBLE);

        public final SqlColumn<Double> latitude = column("latitude", JDBCType.DOUBLE);

        public final SqlColumn<String> photo = column("photo", JDBCType.VARCHAR);

        public final SqlColumn<String> otwCode = column("otw_code", JDBCType.VARCHAR);

        public ShowStartCity() {
            super("show_start_city", ShowStartCity::new);
        }
    }
}