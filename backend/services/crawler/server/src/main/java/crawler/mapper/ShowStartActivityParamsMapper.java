package crawler.mapper;

import static crawler.mapper.ShowStartActivityParamsDynamicSqlSupport.*;

import crawler.entity.ShowStartActivityParams;
import crawler.entity.typehandler.ActivityParamsSitesTypeHandler;
import crawler.entity.typehandler.ActivityParamsStylesTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ShowStartActivityParamsMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonInsertMapper<ShowStartActivityParams>, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    BasicColumn[] selectList = BasicColumn.columnList(cityCode, cityName, styles, sites);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ShowStartActivityParamsResult", value = {
        @Result(column="city_code", property="cityCode", jdbcType=JdbcType.INTEGER),
        @Result(column="city_name", property="cityName", jdbcType=JdbcType.VARCHAR),
        @Result(column="styles", property="styles", typeHandler=ActivityParamsStylesTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="sites", property="sites", typeHandler=ActivityParamsSitesTypeHandler.class, jdbcType=JdbcType.OTHER)
    })
    List<ShowStartActivityParams> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ShowStartActivityParamsResult")
    Optional<ShowStartActivityParams> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default int insertMultiple(Collection<ShowStartActivityParams> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, showStartActivityParams, c ->
            c.map(cityCode).toProperty("cityCode")
            .map(cityName).toProperty("cityName")
            .map(styles).toProperty("styles")
            .map(sites).toProperty("sites")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default int insertSelective(ShowStartActivityParams row) {
        return MyBatis3Utils.insert(this::insert, row, showStartActivityParams, c ->
            c.map(cityCode).toPropertyWhenPresent("cityCode", row::getCityCode)
            .map(cityName).toPropertyWhenPresent("cityName", row::getCityName)
            .map(styles).toPropertyWhenPresent("styles", row::getStyles)
            .map(sites).toPropertyWhenPresent("sites", row::getSites)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default Optional<ShowStartActivityParams> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default List<ShowStartActivityParams> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default List<ShowStartActivityParams> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, showStartActivityParams, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    static UpdateDSL<UpdateModel> updateAllColumns(ShowStartActivityParams row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(cityCode).equalTo(row::getCityCode)
                .set(cityName).equalTo(row::getCityName)
                .set(styles).equalTo(row::getStyles)
                .set(sites).equalTo(row::getSites);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ShowStartActivityParams row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(cityCode).equalToWhenPresent(row::getCityCode)
                .set(cityName).equalToWhenPresent(row::getCityName)
                .set(styles).equalToWhenPresent(row::getStyles)
                .set(sites).equalToWhenPresent(row::getSites);
    }
}