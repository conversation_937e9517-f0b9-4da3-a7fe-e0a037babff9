package crawler.mapper;

import static crawler.mapper.ShowStartActivityDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import crawler.entity.ShowStartActivity;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ShowStartActivityMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonInsertMapper<ShowStartActivity>, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    BasicColumn[] selectList = BasicColumn.columnList(id, title, poster, isExclusive, performers, price, showTime, siteName, cityName, isGroup, soldOut);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ShowStartActivityResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="title", property="title", jdbcType=JdbcType.VARCHAR),
        @Result(column="poster", property="poster", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_exclusive", property="isExclusive", jdbcType=JdbcType.INTEGER),
        @Result(column="performers", property="performers", jdbcType=JdbcType.VARCHAR),
        @Result(column="price", property="price", jdbcType=JdbcType.VARCHAR),
        @Result(column="show_time", property="showTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
        @Result(column="city_name", property="cityName", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_group", property="isGroup", jdbcType=JdbcType.INTEGER),
        @Result(column="sold_out", property="soldOut", jdbcType=JdbcType.INTEGER)
    })
    List<ShowStartActivity> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ShowStartActivityResult")
    Optional<ShowStartActivity> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int insertMultiple(Collection<ShowStartActivity> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, showStartActivity, c ->
            c.map(id).toProperty("id")
            .map(title).toProperty("title")
            .map(poster).toProperty("poster")
            .map(isExclusive).toProperty("isExclusive")
            .map(performers).toProperty("performers")
            .map(price).toProperty("price")
            .map(showTime).toProperty("showTime")
            .map(siteName).toProperty("siteName")
            .map(cityName).toProperty("cityName")
            .map(isGroup).toProperty("isGroup")
            .map(soldOut).toProperty("soldOut")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int insertSelective(ShowStartActivity row) {
        return MyBatis3Utils.insert(this::insert, row, showStartActivity, c ->
            c.map(id).toPropertyWhenPresent("id", row::getId)
            .map(title).toPropertyWhenPresent("title", row::getTitle)
            .map(poster).toPropertyWhenPresent("poster", row::getPoster)
            .map(isExclusive).toPropertyWhenPresent("isExclusive", row::getIsExclusive)
            .map(performers).toPropertyWhenPresent("performers", row::getPerformers)
            .map(price).toPropertyWhenPresent("price", row::getPrice)
            .map(showTime).toPropertyWhenPresent("showTime", row::getShowTime)
            .map(siteName).toPropertyWhenPresent("siteName", row::getSiteName)
            .map(cityName).toPropertyWhenPresent("cityName", row::getCityName)
            .map(isGroup).toPropertyWhenPresent("isGroup", row::getIsGroup)
            .map(soldOut).toPropertyWhenPresent("soldOut", row::getSoldOut)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default Optional<ShowStartActivity> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default List<ShowStartActivity> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default List<ShowStartActivity> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default Optional<ShowStartActivity> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, showStartActivity, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    static UpdateDSL<UpdateModel> updateAllColumns(ShowStartActivity row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(title).equalTo(row::getTitle)
                .set(poster).equalTo(row::getPoster)
                .set(isExclusive).equalTo(row::getIsExclusive)
                .set(performers).equalTo(row::getPerformers)
                .set(price).equalTo(row::getPrice)
                .set(showTime).equalTo(row::getShowTime)
                .set(siteName).equalTo(row::getSiteName)
                .set(cityName).equalTo(row::getCityName)
                .set(isGroup).equalTo(row::getIsGroup)
                .set(soldOut).equalTo(row::getSoldOut);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ShowStartActivity row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(title).equalToWhenPresent(row::getTitle)
                .set(poster).equalToWhenPresent(row::getPoster)
                .set(isExclusive).equalToWhenPresent(row::getIsExclusive)
                .set(performers).equalToWhenPresent(row::getPerformers)
                .set(price).equalToWhenPresent(row::getPrice)
                .set(showTime).equalToWhenPresent(row::getShowTime)
                .set(siteName).equalToWhenPresent(row::getSiteName)
                .set(cityName).equalToWhenPresent(row::getCityName)
                .set(isGroup).equalToWhenPresent(row::getIsGroup)
                .set(soldOut).equalToWhenPresent(row::getSoldOut);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    default int updateByPrimaryKeySelective(ShowStartActivity row) {
        return update(c ->
            c.set(title).equalToWhenPresent(row::getTitle)
            .set(poster).equalToWhenPresent(row::getPoster)
            .set(isExclusive).equalToWhenPresent(row::getIsExclusive)
            .set(performers).equalToWhenPresent(row::getPerformers)
            .set(price).equalToWhenPresent(row::getPrice)
            .set(showTime).equalToWhenPresent(row::getShowTime)
            .set(siteName).equalToWhenPresent(row::getSiteName)
            .set(cityName).equalToWhenPresent(row::getCityName)
            .set(isGroup).equalToWhenPresent(row::getIsGroup)
            .set(soldOut).equalToWhenPresent(row::getSoldOut)
            .where(id, isEqualTo(row::getId))
        );
    }
}