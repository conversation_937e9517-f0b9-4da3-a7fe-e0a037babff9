package crawler.service;

import static crawler.mapper.ShowStartActivityDynamicSqlSupport.showStartActivity;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.delete.DeleteDSLCompleter.allRows;

import igigdb.lib.core.util.Tx;
import crawler.showstart.ShowStartCrawler;
import crawler.entity.ShowStartActivity;
import crawler.entity.ShowStartActivityParams;
import crawler.entity.ShowStartCity;
import crawler.mapper.ShowStartActivityMapper;
import crawler.mapper.ShowStartActivityParamsMapper;
import crawler.mapper.ShowStartCityMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 使用无头浏览器爬取秀动网站数据的服务
 *
 * <AUTHOR>
 * @since 2025/4/27
 */
@Service
public class ShowStartService {

    private final ShowStartCrawler showStartCrawler;
    private final ShowStartActivityParamsMapper activityParamsMapper;
    private final ShowStartCityMapper cityMapper;
    private final ShowStartActivityMapper activityMapper;

    public ShowStartService(
            ShowStartCrawler showStartCrawler,
            ShowStartActivityParamsMapper activityParamsMapper,
            ShowStartCityMapper cityMapper,
            ShowStartActivityMapper activityMapper) {
        this.showStartCrawler = showStartCrawler;
        this.activityParamsMapper = activityParamsMapper;
        this.cityMapper = cityMapper;
        this.activityMapper = activityMapper;
    }

    /**
     * 爬取所有 activity params 数据并保存到数据库
     *
     * @return activity params 列表
     */
    public List<ShowStartActivityParams> crawlActivityParams() {

        var activityParams = showStartCrawler.crawlActivityParams();

        if (!activityParams.isEmpty()) {
            Tx.exec(() -> {
                activityParamsMapper.delete(allRows());

                for (var params : activityParams) {
                    createActivityParams(params);
                }
            });
        }

        return activityParams;
    }

    /**
     * 爬取所有城市数据并保存到数据库
     *
     * @return 城市列表
     */
    public List<ShowStartCity> crawlCities() {
        var cities = showStartCrawler.crawlCities();

        if (!cities.isEmpty()) {
            Tx.exec(() -> {
                cityMapper.delete(c -> c.where(
                        showStartActivity.id,
                        isIn(cities.stream().map(ShowStartCity::getId).toList())));

                for (var city : cities) {
                    createCity(city);
                }
            });
        }

        return cities;
    }

    /**
     * 爬取指定 city 的活动数据并保存到数据库
     *
     * @param cityCode 城市代码
     * @return 活动列表
     */
    public List<ShowStartActivity> crawlActivities(String cityCode) {
        var activities = showStartCrawler.crawlActivities(cityCode);

        if (!activities.isEmpty()) {
            Tx.exec(() -> {
                // 先删除再插入
                activityMapper.delete(c -> c.where(
                        showStartActivity.id,
                        isIn(activities.stream().map(ShowStartActivity::getId).toList())));

                for (var activity : activities) {
                    createActivity(activity);
                }
            });
        }

        return activities;
    }

    private long createActivity(ShowStartActivity activity) {

        activityMapper.insertSelective(activity);

        return activity.getId();
    }

    private long createCity(ShowStartCity city) {

        cityMapper.insertSelective(city);

        return city.getId();
    }

    private void createActivityParams(ShowStartActivityParams activityParams) {

        activityParamsMapper.insertSelective(activityParams);
    }
}
