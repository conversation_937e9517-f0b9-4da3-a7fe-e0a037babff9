package crawler.internal;

import grpcstarter.server.GrpcService;
import igigdb.crawler.v1.CrawlShowStartRequest;
import igigdb.crawler.v1.CrawlShowStartResponse;
import igigdb.crawler.v1.ShowStartServiceGrpc;
import igigdb.lib.core.util.ThreadPool;
import crawler.entity.ShowStartCity;
import crawler.service.ShowStartService;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@GrpcService
public class ShowStartInternalServer extends ShowStartServiceGrpc.ShowStartServiceImplBase {
    private static final Logger log = LoggerFactory.getLogger(ShowStartInternalServer.class);

    private final ShowStartService showStartService;

    public ShowStartInternalServer(ShowStartService showStartService) {
        this.showStartService = showStartService;
    }

    @Override
    public void crawlShowStart(CrawlShowStartRequest request, StreamObserver<CrawlShowStartResponse> responseObserver) {

        ThreadPool.exec(this::doCrawlShowStartData);

        responseObserver.onNext(CrawlShowStartResponse.newBuilder().build());
        responseObserver.onCompleted();
    }

    private void doCrawlShowStartData() {
        // 由小到大爬，方便从指定位置恢复
        var cities = showStartService.crawlCities().stream()
                .sorted(Comparator.comparing(ShowStartInternalServer::getCode))
                .toList();

        for (var city : cities) {
            log.info("开始爬取城市：{}, code: {}", city.getName(), city.getCode());
            showStartService.crawlActivities(city.getCode());
        }
    }

    private static int getCode(ShowStartCity city) {
        try {
            return Integer.parseInt(city.getCode());
        } catch (NumberFormatException e) {
            return Integer.MAX_VALUE;
        }
    }
}
