package crawler.mapper;

import crawler.entity.model.ShowStartActivityParamsSite;
import crawler.entity.model.ShowStartActivityParamsStyle;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ShowStartActivityParamsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    public static final ShowStartActivityParams showStartActivityParams = new ShowStartActivityParams();

    /**
     * Database Column Remarks:
     *   城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_code")
    public static final SqlColumn<Integer> cityCode = showStartActivityParams.cityCode;

    /**
     * Database Column Remarks:
     *   城市名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_name")
    public static final SqlColumn<String> cityName = showStartActivityParams.cityName;

    /**
     * Database Column Remarks:
     *   音乐风格列表，JSON格式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.styles")
    public static final SqlColumn<List<ShowStartActivityParamsStyle>> styles = showStartActivityParams.styles;

    /**
     * Database Column Remarks:
     *   演出场地列表，JSON格式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.sites")
    public static final SqlColumn<List<ShowStartActivityParamsSite>> sites = showStartActivityParams.sites;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    public static final class ShowStartActivityParams extends AliasableSqlTable<ShowStartActivityParams> {
        public final SqlColumn<Integer> cityCode = column("city_code", JDBCType.INTEGER);

        public final SqlColumn<String> cityName = column("city_name", JDBCType.VARCHAR);

        public final SqlColumn<List<ShowStartActivityParamsStyle>> styles = column("styles", JDBCType.OTHER, "igigdb.service_crawler_server.entity.typehandler.ActivityParamsStylesTypeHandler");

        public final SqlColumn<List<ShowStartActivityParamsSite>> sites = column("sites", JDBCType.OTHER, "igigdb.service_crawler_server.entity.typehandler.ActivityParamsSitesTypeHandler");

        public ShowStartActivityParams() {
            super("show_start_activity_params", ShowStartActivityParams::new);
        }
    }
}