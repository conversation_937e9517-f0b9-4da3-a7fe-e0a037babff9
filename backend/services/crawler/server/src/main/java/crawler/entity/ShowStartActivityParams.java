package crawler.entity;

import crawler.entity.model.ShowStartActivityParamsSite;
import crawler.entity.model.ShowStartActivityParamsStyle;
import jakarta.annotation.Generated;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table show_start_activity_params
 */
public class ShowStartActivityParams {
    /**
     * Database Column Remarks:
     *   城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_code")
    private Integer cityCode;

    /**
     * Database Column Remarks:
     *   城市名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_name")
    private String cityName;

    /**
     * Database Column Remarks:
     *   音乐风格列表，JSON格式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.styles")
    private List<ShowStartActivityParamsStyle> styles;

    /**
     * Database Column Remarks:
     *   演出场地列表，JSON格式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.sites")
    private List<ShowStartActivityParamsSite> sites;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_code")
    public Integer getCityCode() {
        return cityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_code")
    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_name")
    public String getCityName() {
        return cityName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.city_name")
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.styles")
    public List<ShowStartActivityParamsStyle> getStyles() {
        return styles;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.styles")
    public void setStyles(List<ShowStartActivityParamsStyle> styles) {
        this.styles = styles;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.sites")
    public List<ShowStartActivityParamsSite> getSites() {
        return sites;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity_params.sites")
    public void setSites(List<ShowStartActivityParamsSite> sites) {
        this.sites = sites;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", styles=").append(styles);
        sb.append(", sites=").append(sites);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShowStartActivityParams other = (ShowStartActivityParams) that;
        return (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getStyles() == null ? other.getStyles() == null : this.getStyles().equals(other.getStyles()))
            && (this.getSites() == null ? other.getSites() == null : this.getSites().equals(other.getSites()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity_params")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getStyles() == null) ? 0 : getStyles().hashCode());
        result = prime * result + ((getSites() == null) ? 0 : getSites().hashCode());
        return result;
    }
}