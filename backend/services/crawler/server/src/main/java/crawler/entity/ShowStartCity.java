package crawler.entity;

import jakarta.annotation.Generated;
import org.jspecify.annotations.Nullable;

/**
 * Database Table Remarks:
 *   Stores city information from Showstart
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table show_start_city
 */
public class ShowStartCity {
    /**
     * Database Column Remarks:
     *   城市ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   国家代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.country_code")
    private String countryCode;

    /**
     * Database Column Remarks:
     *   省份代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.province_code")
    private String provinceCode;

    /**
     * Database Column Remarks:
     *   城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.code")
    private String code;

    /**
     * Database Column Remarks:
     *   百度地图城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.baidu_code")
    @Nullable
    private String baiduCode;

    /**
     * Database Column Remarks:
     *   行政区划代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.administrative_code")
    private String administrativeCode;

    /**
     * Database Column Remarks:
     *   城市名称（中文）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name")
    private String name;

    /**
     * Database Column Remarks:
     *   城市名称（英文大写）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name_en")
    private String nameEn;

    /**
     * Database Column Remarks:
     *   城市名称（英文）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.en")
    private String en;

    /**
     * Database Column Remarks:
     *   是否在首页显示（1表示显示，0表示不显示）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display")
    private Integer homeDisplay;

    /**
     * Database Column Remarks:
     *   首页显示枚举值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display_enum")
    private String homeDisplayEnum;

    /**
     * Database Column Remarks:
     *   附近城市代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.nearby_city")
    @Nullable
    private String nearbyCity;

    /**
     * Database Column Remarks:
     *   是否热门城市（1表示是，0表示否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.is_hot")
    private Integer isHot;

    /**
     * Database Column Remarks:
     *   热门城市排序
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.hot_sort")
    @Nullable
    private Integer hotSort;

    /**
     * Database Column Remarks:
     *   经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.longitude")
    private Double longitude;

    /**
     * Database Column Remarks:
     *   纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.latitude")
    private Double latitude;

    /**
     * Database Column Remarks:
     *   城市图片URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.photo")
    @Nullable
    private String photo;

    /**
     * Database Column Remarks:
     *   OTW代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.otw_code")
    @Nullable
    private String otwCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.country_code")
    public String getCountryCode() {
        return countryCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.country_code")
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.province_code")
    public String getProvinceCode() {
        return provinceCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.province_code")
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.code")
    public String getCode() {
        return code;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.code")
    public void setCode(String code) {
        this.code = code;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.baidu_code")
    @Nullable
    public String getBaiduCode() {
        return baiduCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.baidu_code")
    public void setBaiduCode(@Nullable String baiduCode) {
        this.baiduCode = baiduCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.administrative_code")
    public String getAdministrativeCode() {
        return administrativeCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.administrative_code")
    public void setAdministrativeCode(String administrativeCode) {
        this.administrativeCode = administrativeCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name")
    public void setName(String name) {
        this.name = name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name_en")
    public String getNameEn() {
        return nameEn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.name_en")
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.en")
    public String getEn() {
        return en;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.en")
    public void setEn(String en) {
        this.en = en;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display")
    public Integer getHomeDisplay() {
        return homeDisplay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display")
    public void setHomeDisplay(Integer homeDisplay) {
        this.homeDisplay = homeDisplay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display_enum")
    public String getHomeDisplayEnum() {
        return homeDisplayEnum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.home_display_enum")
    public void setHomeDisplayEnum(String homeDisplayEnum) {
        this.homeDisplayEnum = homeDisplayEnum;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.nearby_city")
    @Nullable
    public String getNearbyCity() {
        return nearbyCity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.nearby_city")
    public void setNearbyCity(@Nullable String nearbyCity) {
        this.nearbyCity = nearbyCity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.is_hot")
    public Integer getIsHot() {
        return isHot;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.is_hot")
    public void setIsHot(Integer isHot) {
        this.isHot = isHot;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.hot_sort")
    @Nullable
    public Integer getHotSort() {
        return hotSort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.hot_sort")
    public void setHotSort(@Nullable Integer hotSort) {
        this.hotSort = hotSort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.longitude")
    public Double getLongitude() {
        return longitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.longitude")
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.latitude")
    public Double getLatitude() {
        return latitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.latitude")
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.photo")
    @Nullable
    public String getPhoto() {
        return photo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.photo")
    public void setPhoto(@Nullable String photo) {
        this.photo = photo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.otw_code")
    @Nullable
    public String getOtwCode() {
        return otwCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_city.otw_code")
    public void setOtwCode(@Nullable String otwCode) {
        this.otwCode = otwCode;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", countryCode=").append(countryCode);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", code=").append(code);
        sb.append(", baiduCode=").append(baiduCode);
        sb.append(", administrativeCode=").append(administrativeCode);
        sb.append(", name=").append(name);
        sb.append(", nameEn=").append(nameEn);
        sb.append(", en=").append(en);
        sb.append(", homeDisplay=").append(homeDisplay);
        sb.append(", homeDisplayEnum=").append(homeDisplayEnum);
        sb.append(", nearbyCity=").append(nearbyCity);
        sb.append(", isHot=").append(isHot);
        sb.append(", hotSort=").append(hotSort);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", photo=").append(photo);
        sb.append(", otwCode=").append(otwCode);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShowStartCity other = (ShowStartCity) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCountryCode() == null ? other.getCountryCode() == null : this.getCountryCode().equals(other.getCountryCode()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getBaiduCode() == null ? other.getBaiduCode() == null : this.getBaiduCode().equals(other.getBaiduCode()))
            && (this.getAdministrativeCode() == null ? other.getAdministrativeCode() == null : this.getAdministrativeCode().equals(other.getAdministrativeCode()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getNameEn() == null ? other.getNameEn() == null : this.getNameEn().equals(other.getNameEn()))
            && (this.getEn() == null ? other.getEn() == null : this.getEn().equals(other.getEn()))
            && (this.getHomeDisplay() == null ? other.getHomeDisplay() == null : this.getHomeDisplay().equals(other.getHomeDisplay()))
            && (this.getHomeDisplayEnum() == null ? other.getHomeDisplayEnum() == null : this.getHomeDisplayEnum().equals(other.getHomeDisplayEnum()))
            && (this.getNearbyCity() == null ? other.getNearbyCity() == null : this.getNearbyCity().equals(other.getNearbyCity()))
            && (this.getIsHot() == null ? other.getIsHot() == null : this.getIsHot().equals(other.getIsHot()))
            && (this.getHotSort() == null ? other.getHotSort() == null : this.getHotSort().equals(other.getHotSort()))
            && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
            && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
            && (this.getPhoto() == null ? other.getPhoto() == null : this.getPhoto().equals(other.getPhoto()))
            && (this.getOtwCode() == null ? other.getOtwCode() == null : this.getOtwCode().equals(other.getOtwCode()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_city")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCountryCode() == null) ? 0 : getCountryCode().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getBaiduCode() == null) ? 0 : getBaiduCode().hashCode());
        result = prime * result + ((getAdministrativeCode() == null) ? 0 : getAdministrativeCode().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getNameEn() == null) ? 0 : getNameEn().hashCode());
        result = prime * result + ((getEn() == null) ? 0 : getEn().hashCode());
        result = prime * result + ((getHomeDisplay() == null) ? 0 : getHomeDisplay().hashCode());
        result = prime * result + ((getHomeDisplayEnum() == null) ? 0 : getHomeDisplayEnum().hashCode());
        result = prime * result + ((getNearbyCity() == null) ? 0 : getNearbyCity().hashCode());
        result = prime * result + ((getIsHot() == null) ? 0 : getIsHot().hashCode());
        result = prime * result + ((getHotSort() == null) ? 0 : getHotSort().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getPhoto() == null) ? 0 : getPhoto().hashCode());
        result = prime * result + ((getOtwCode() == null) ? 0 : getOtwCode().hashCode());
        return result;
    }
}