package crawler.entity;

import jakarta.annotation.Generated;
import org.jspecify.annotations.Nullable;

/**
 * Database Table Remarks:
 *   Stores activity (performance) information from Showstart - 存储从Showstart获取的演出活动信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table show_start_activity
 */
public class ShowStartActivity {
    /**
     * Database Column Remarks:
     *   活动ID - 唯一标识符，来自Showstart的原始ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   活动标题 - 演出活动的完整名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.title")
    private String title;

    /**
     * Database Column Remarks:
     *   海报图片URL - 活动海报图片的完整URL地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.poster")
    private String poster;

    /**
     * Database Column Remarks:
     *   是否独家 - 标识活动是否为独家演出（2表示否，1表示是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_exclusive")
    private Integer isExclusive;

    /**
     * Database Column Remarks:
     *   表演者/艺术家 - 参与演出的艺术家或表演者名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.performers")
    @Nullable
    private String performers;

    /**
     * Database Column Remarks:
     *   价格 - 演出票价信息，通常包含货币符号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.price")
    private String price;

    /**
     * Database Column Remarks:
     *   演出时间 - 活动开始的具体日期和时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.show_time")
    private String showTime;

    /**
     * Database Column Remarks:
     *   演出场地名称 - 活动举办的具体场馆名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.site_name")
    private String siteName;

    /**
     * Database Column Remarks:
     *   城市名称 - 活动举办的城市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.city_name")
    private String cityName;

    /**
     * Database Column Remarks:
     *   是否团购 - 标识活动是否支持团购（1表示是，0表示否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_group")
    private Integer isGroup;

    /**
     * Database Column Remarks:
     *   是否售罄 - 标识活动票是否已售罄（0表示售罄，1表示未售罄）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.sold_out")
    private Integer soldOut;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.title")
    public String getTitle() {
        return title;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.title")
    public void setTitle(String title) {
        this.title = title;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.poster")
    public String getPoster() {
        return poster;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.poster")
    public void setPoster(String poster) {
        this.poster = poster;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_exclusive")
    public Integer getIsExclusive() {
        return isExclusive;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_exclusive")
    public void setIsExclusive(Integer isExclusive) {
        this.isExclusive = isExclusive;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.performers")
    @Nullable
    public String getPerformers() {
        return performers;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.performers")
    public void setPerformers(@Nullable String performers) {
        this.performers = performers;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.price")
    public String getPrice() {
        return price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.price")
    public void setPrice(String price) {
        this.price = price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.show_time")
    public String getShowTime() {
        return showTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.show_time")
    public void setShowTime(String showTime) {
        this.showTime = showTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.site_name")
    public String getSiteName() {
        return siteName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.site_name")
    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.city_name")
    public String getCityName() {
        return cityName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.city_name")
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_group")
    public Integer getIsGroup() {
        return isGroup;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.is_group")
    public void setIsGroup(Integer isGroup) {
        this.isGroup = isGroup;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.sold_out")
    public Integer getSoldOut() {
        return soldOut;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: show_start_activity.sold_out")
    public void setSoldOut(Integer soldOut) {
        this.soldOut = soldOut;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", poster=").append(poster);
        sb.append(", isExclusive=").append(isExclusive);
        sb.append(", performers=").append(performers);
        sb.append(", price=").append(price);
        sb.append(", showTime=").append(showTime);
        sb.append(", siteName=").append(siteName);
        sb.append(", cityName=").append(cityName);
        sb.append(", isGroup=").append(isGroup);
        sb.append(", soldOut=").append(soldOut);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShowStartActivity other = (ShowStartActivity) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getPoster() == null ? other.getPoster() == null : this.getPoster().equals(other.getPoster()))
            && (this.getIsExclusive() == null ? other.getIsExclusive() == null : this.getIsExclusive().equals(other.getIsExclusive()))
            && (this.getPerformers() == null ? other.getPerformers() == null : this.getPerformers().equals(other.getPerformers()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getShowTime() == null ? other.getShowTime() == null : this.getShowTime().equals(other.getShowTime()))
            && (this.getSiteName() == null ? other.getSiteName() == null : this.getSiteName().equals(other.getSiteName()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getIsGroup() == null ? other.getIsGroup() == null : this.getIsGroup().equals(other.getIsGroup()))
            && (this.getSoldOut() == null ? other.getSoldOut() == null : this.getSoldOut().equals(other.getSoldOut()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: show_start_activity")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getPoster() == null) ? 0 : getPoster().hashCode());
        result = prime * result + ((getIsExclusive() == null) ? 0 : getIsExclusive().hashCode());
        result = prime * result + ((getPerformers() == null) ? 0 : getPerformers().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getShowTime() == null) ? 0 : getShowTime().hashCode());
        result = prime * result + ((getSiteName() == null) ? 0 : getSiteName().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getIsGroup() == null) ? 0 : getIsGroup().hashCode());
        result = prime * result + ((getSoldOut() == null) ? 0 : getSoldOut().hashCode());
        return result;
    }
}