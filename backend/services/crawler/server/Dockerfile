# https://github.com/microsoft/playwright-java/tree/main/utils/docker
# version sync with gradle.properties
FROM --platform=linux/amd64 mcr.microsoft.com/playwright/java:v1.54.0

COPY build/libs/app.jar app.jar

# extract
# app
# ├── app.jar
# └── lib
RUN java -Djarmode=tools -jar app.jar extract --destination app
RUN rm app.jar

WORKDIR /app

RUN java -XX:ArchiveClassesAtExit=app.jsa -Dtraining=1 -jar app.jar

ARG JAVA_OPTS="-XX:InitialRAMPercentage=75.0 -XX:MinRAMPercentage=75.0 -XX:MaxRAMPercentage=75.0"

ENV TZ=UTC

EXPOSE 9090

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -XX:SharedArchiveFile=app.jsa -jar app.jar"]
