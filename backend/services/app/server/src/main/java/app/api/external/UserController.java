package app.api.external;

import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/5/16
 */
@RestController
public class UserController {
    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @GetMapping("/api/service-app/v1/users")
    public Map<String, List<String>> user(@RequestHeader HttpHeaders headers) {

        log.info("headers: {}", headers);

        return Map.copyOf(headers);
    }
}
