package app.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class UserDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: user")
    public static final User user = new User();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.id")
    public static final SqlColumn<Long> id = user.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.username")
    public static final SqlColumn<String> username = user.username;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.email")
    public static final SqlColumn<String> email = user.email;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.password_hash")
    public static final SqlColumn<String> passwordHash = user.passwordHash;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.first_name")
    public static final SqlColumn<String> firstName = user.firstName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.last_name")
    public static final SqlColumn<String> lastName = user.lastName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.is_active")
    public static final SqlColumn<Boolean> isActive = user.isActive;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.is_verified")
    public static final SqlColumn<Boolean> isVerified = user.isVerified;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = user.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: user.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = user.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: user")
    public static final class User extends AliasableSqlTable<User> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> username = column("username", JDBCType.VARCHAR);

        public final SqlColumn<String> email = column("email", JDBCType.VARCHAR);

        public final SqlColumn<String> passwordHash = column("password_hash", JDBCType.VARCHAR);

        public final SqlColumn<String> firstName = column("first_name", JDBCType.VARCHAR);

        public final SqlColumn<String> lastName = column("last_name", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> isActive = column("is_active", JDBCType.BIT);

        public final SqlColumn<Boolean> isVerified = column("is_verified", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public User() {
            super("user", User::new);
        }
    }
}