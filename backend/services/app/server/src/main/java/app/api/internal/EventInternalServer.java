package app.api.internal;

import grpcstarter.server.GrpcService;
import igigdb.app.v1.CreateEventRequest;
import igigdb.app.v1.CreateEventResponse;
import igigdb.app.v1.EventServiceGrpc;
import app.service.EventService;
import io.grpc.stub.StreamObserver;

/**
 * <AUTHOR>
 * @since 2025/5/18
 */
@GrpcService
public class EventInternalServer extends EventServiceGrpc.EventServiceImplBase {

    private final EventService eventService;

    public EventInternalServer(EventService eventService) {
        this.eventService = eventService;
    }

    @Override
    public void createEvent(CreateEventRequest request, StreamObserver<CreateEventResponse> responseObserver) {
        super.createEvent(request, responseObserver);
    }
}
