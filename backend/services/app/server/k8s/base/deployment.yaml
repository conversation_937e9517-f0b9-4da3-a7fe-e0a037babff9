apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${SERVICE}
  labels:
    service: ${SERVICE}
    branch: ${BRANCH}
    commit: ${COMMIT}
    datetime: ${DATETIME}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      service: ${SERVICE}
      branch: ${BRANCH}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
        sidecar.istio.io/proxyCPU: "50m"
        sidecar.istio.io/proxyMemory: "64Mi"
      labels:
        service: ${SERVICE}
        branch: ${BRANCH}
        commit: ${COMMIT}
        datetime: ${DATETIME}
    spec:
      containers:
        - name: ${SERVICE}
          image: ${IMAGE}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: 2000m
              memory: 512Mi
          ports:
            - containerPort: 8080
          startupProbe:
            httpGet:
              path: /healthz?service=startup
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 2
            failureThreshold: 30 # 允许较长的启动时间 (5s * 30 = 150s)
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /healthz?service=readiness
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 2
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: /healthz?service=liveness
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 2
            successThreshold: 1
