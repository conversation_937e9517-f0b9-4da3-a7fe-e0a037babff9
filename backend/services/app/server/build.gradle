plugins{
    id "org.springframework.boot"
}

dependencies {
    // lib
    implementation(project(":lib-java:core"))
    implementation(project(":lib-java:mybatis"))
    implementation(project(":lib-java:springdoc"))

    // api
    implementation(project(":proto:igigdb:app"))
    implementation(project(":proto:igigdb:crawler"))

    // web
    implementation("org.springframework.boot:spring-boot-starter-web")

    // grpc
    implementation("io.github.danielliu1123:grpc-boot-starter")

    // db
    implementation("org.postgresql:postgresql")

    // mybatis
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")

    // test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

apply from: "${rootDir}/gradle/mybatis-generator.gradle"

bootJar {
    archiveBaseName = "app"
}
