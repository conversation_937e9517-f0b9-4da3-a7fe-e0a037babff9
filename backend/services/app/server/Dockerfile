FROM amazoncorretto:21 AS builder

WORKDIR /builder

COPY build/libs/app.jar app.jar

# extract
# app
# ├── app.jar
# └── lib
RUN java -Djarmode=tools -jar app.jar extract --destination app

# cd app
WORKDIR /builder/app

# CDS training run
# app
# ├── app.jar
# ├── app.jsa
# └── lib
RUN java -XX:ArchiveClassesAtExit=app.jsa -Dtraining=1 -jar app.jar


FROM amazoncorretto:21

WORKDIR /app

COPY --from=builder /builder/app/ .

ARG JAVA_OPTS="-XX:InitialRAMPercentage=75.0 -XX:MinRAMPercentage=75.0 -XX:MaxRAMPercentage=75.0"

ENV TZ=UTC

EXPOSE 8080

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -XX:SharedArchiveFile=app.jsa -jar app.jar"]
