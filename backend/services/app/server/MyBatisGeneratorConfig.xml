<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="Postgres">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value='"'/>
        <property name="endingDelimiter" value='"'/>
        <property name="autoDelimitKeywords" value='true'/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="igigdb.lib.mybatis.plugin.NullableColumnPlugin"/>
        <plugin type="igigdb.lib.mybatis.plugin.DeprecatedColumnsPlugin"/>
        <plugin type="igigdb.lib.mybatis.plugin.DisableGeneratedMapperMethodsPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="org.postgresql.Driver"
                        connectionURL="***********************************************************************************"
                        userId="avnadmin" password="AVNS_TJtbMlDxqS6fiaIY-Ij"/>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
            <!-- https://mybatis.org/generator/configreference/javaTypeResolver.html -->
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <javaModelGenerator
                targetPackage="app.entity"
                targetProject="src/main/java"/>

        <javaClientGenerator
                targetPackage="app.mapper"
                targetProject="src/main/java">
            <property name="rootInterface"
                      value="org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper"/>
        </javaClientGenerator>

        <!-- 不要注释下面的 table！！！每次都全量生成，保证代码和表结构同步 -->
        <table tableName="user">
            <generatedKey column="id" sqlStatement="JDBC"
                          identity="true"/>
        </table>

    </context>
</generatorConfiguration>
