# App Server

## Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/app/server
gradle bootRun
```

## Gen

```bash
cd $(git rev-parse --show-toplevel)/backend/services/app/server
gradle mbGenerator
```

## Docker

### Build

```bash
cd $(git rev-parse --show-toplevel)/backend/services/app/server
docker build -t app .
```

### Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/app/server
docker run -p 8080:8080 app
```
