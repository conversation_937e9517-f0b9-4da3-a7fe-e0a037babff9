plugins{
    id "org.springframework.boot"
}

dependencies {
    // lib
    implementation(project(":lib-java:core"))
    implementation(project(":lib-java:mybatis"))

    // api
    implementation(project(":proto:igigdb:router"))

    // grpc
    implementation("io.github.danielliu1123:grpc-server-boot-starter")

    // web
    implementation("org.springframework.boot:spring-boot-starter-web")

    // db
    implementation("org.postgresql:postgresql")

    // mybatis
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")

    // test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

apply from: "${rootDir}/gradle/mybatis-generator.gradle"

bootJar {
    archiveBaseName = "app"
}
