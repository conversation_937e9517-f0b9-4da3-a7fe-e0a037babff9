package router.mapper;

import igigdb.router.v1.RouteModel.ServiceConfig;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RouteDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: route")
    public static final Route route = new Route();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.id")
    public static final SqlColumn<Long> id = route.id;

    /**
     * Database Column Remarks:
     *   uniq route name
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.name")
    public static final SqlColumn<String> name = route.name;

    /**
     * Database Column Remarks:
     *   route description
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.description")
    public static final SqlColumn<String> description = route.description;

    /**
     * Database Column Remarks:
     *   service configs
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.service_configs")
    public static final SqlColumn<List<ServiceConfig>> serviceConfigs = route.serviceConfigs;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = route.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = route.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: route.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = route.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: route")
    public static final class Route extends AliasableSqlTable<Route> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("\"name\"", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<List<ServiceConfig>> serviceConfigs = column("service_configs", JDBCType.OTHER, "igigdb.service_router_server.entity.typehandler.RouteServiceConfigsTypeHandler");

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public Route() {
            super("route", Route::new);
        }
    }
}