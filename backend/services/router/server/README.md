# Router Server

## Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/router/server
gradle bootRun
```

## Gen

```bash
cd $(git rev-parse --show-toplevel)/backend/services/router/server
gradle mbGenerator
```

## Docker

### Build

```bash
cd $(git rev-parse --show-toplevel)/backend/services/router/server

docker build -t router .
```

### Run

```bash
cd $(git rev-parse --show-toplevel)/backend/services/router/server
docker run -p 8080:8080 router
```
