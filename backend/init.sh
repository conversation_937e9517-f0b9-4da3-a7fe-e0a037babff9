#!/usr/bin/env bash

# install brew if not installed
if ! command -v brew &> /dev/null
then
  echo "brew could not be found, installing..."
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# install sdkman if not installed
if [[ ! -d "$HOME/.sdkman" ]]; then
  echo "sdkman could not be found, installing..."
  curl -s "https://get.sdkman.io" | bash
fi
source "$HOME/.sdkman/bin/sdkman-init.sh"

brew install go protobuf bufbuild/buf/buf protoc-gen-go protoc-gen-go-grpc

# install golangci-lint
# see https://golangci-lint.run/welcome/install/#binaries
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/HEAD/install.sh | sh -s -- -b "$(go env GOPATH)/bin" v2.3.1

# install java & gradle
java_version=21.0.8-amzn && sdk install java ${java_version} && sdk default java ${java_version}
gradle_verison=9.0.0 && sdk install gradle ${gradle_verison} && sdk default gradle ${gradle_verison}
