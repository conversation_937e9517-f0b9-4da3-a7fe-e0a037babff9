// 用法：
// 1. 在子 module 的 build.gradle 中添加 `apply from: "${rootDir}/gradle/grpc.gradle"`
// 2. proto 文件统一放在 ${rootDir}/proto 目录下
// 3. 运行 `gradle generateProto`

apply plugin: "com.google.protobuf"

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all().configureEach { task ->
            task.plugins {
                grpc {
                    option "@generated=omit"
                }
            }
        }
    }
}

//sourceSets {
//    main {
//        proto {
//            srcDir "${rootDir}/proto"
//        }
//    }
//}
