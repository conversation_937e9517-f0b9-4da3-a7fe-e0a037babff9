// 用法：
// 1. 在子 module 的 build.gradle 中添加 `apply from: "${rootDir}/gradle/mybatis-generator.gradle"`
// 2. 在子 module 的根目录下创建 MyBatisGeneratorConfig.xml
// 3. 运行 `gradle mbGenerator`

apply plugin: "com.qqviaja.gradle.MybatisGenerator"

mybatisGenerator {
    verbose = true
    configFile = "${projectDir}/MyBatisGeneratorConfig.xml"

    dependencies {
        mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}"
        mybatisGenerator "org.postgresql:postgresql"
        mybatisGenerator project(":lib-java:mybatis")
    }
}

mbGenerator.dependsOn(":lib-java:mybatis:jar")