plugins {
    id "org.springframework.boot" version "${springBootVersion}" apply false
    id "io.spring.dependency-management" version "${springDependencyManagementVersion}" apply false
    id "com.diffplug.spotless" version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id "com.google.protobuf" version "${protobufGradlePluginVersion}" apply false
    id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}" apply false
}

allprojects {
    apply plugin: "java"
    apply plugin: "java-library"
    apply plugin: "io.spring.dependency-management"
    apply plugin: "com.diffplug.spotless"
    apply plugin: "com.github.spotbugs"
    apply plugin: "checkstyle"

    repositories {
        mavenCentral()
    }

    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << "-parameters"
    }

    compileTestJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << "-parameters"
    }

    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom "io.github.danielliu1123:grpc-starter-dependencies:${grpcStarterVersion}"
            mavenBom "io.grpc:grpc-bom:${grpcVersion}"
            mavenBom "com.google.protobuf:protobuf-bom:${protobufVersion}"
        }
    }

    dependencies {
        compileOnly("org.projectlombok:lombok")
        annotationProcessor("org.projectlombok:lombok")
        testCompileOnly("org.projectlombok:lombok")
        testAnnotationProcessor("org.projectlombok:lombok")
        compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")

        implementation("org.jspecify:jspecify")

        // https://docs.gradle.org/current/userguide/upgrading_version_8.html#test_framework_implementation_dependencies
        testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    }

    test {
        useJUnitPlatform()

        jvmArgs("-XX:+EnableDynamicAgentLoading")
    }

    spotless {
        encoding "UTF-8"
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude(
                    "build/generated/**",
                    "**/entity/*.java",
                    "**/mapper/*Mapper.java",
                    "**/mapper/*DynamicSqlSupport.java"
            )

            custom("Refuse wildcard imports", {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll "FindReturnRef", "MethodReturnCheck", "SerializableIdiom"
        excludeFilter = file("${rootDir}/config/spotbugs/exclude.xml")
    }

    checkstyle {
        toolVersion = "${checkstyleVersion}"
        configDirectory = file("${rootDir}/config/checkstyle")
        configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
        maxErrors = 0
        maxWarnings = 0
        ignoreFailures = false
    }
}
