plugins {
    id "java-library"
    id "com.google.protobuf" version "${protobufGradlePluginVersion}"
}

dependencies {
    api("com.google.protobuf:protobuf-java")
    api("io.grpc:grpc-stub")
    api("io.grpc:grpc-protobuf")
    api("javax.annotation:javax.annotation-api")
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

sourceSets {
    main {
        java {
            srcDirs 'src/main/java'
        }
    }
}