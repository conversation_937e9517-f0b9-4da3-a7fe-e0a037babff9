// @gencoder.generated: {{properties.baseDir}}/{{_replaceAll properties.protoPackage '.' '/'}}/{{_snakeCase table.name}}_service.proto

syntax = "proto3";

package {{properties.protoPackage}};

option go_package = "{{_goPkg properties.protoPackage}}";
option java_multiple_files = true;
option java_package = "{{_javaPkg properties.protoPackage}}";

service {{_pascalCase table.name}}Service {
  rpc Create{{_pascalCase table.name}} (Create{{_pascalCase table.name}}Request) returns (Create{{_pascalCase table.name}}Response);
  rpc Update{{_pascalCase table.name}} (Update{{_pascalCase table.name}}Request) returns (Update{{_pascalCase table.name}}Response);
  rpc Delete{{_pascalCase table.name}} (Delete{{_pascalCase table.name}}Request) returns (Delete{{_pascalCase table.name}}Response);
}

// Create{{_pascalCase table.name}} request
message Create{{_pascalCase table.name}}Request {
{{#each table.columns}}
  {{#if isPrimaryKey}}
  {{else}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  optional {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/if}}
{{/each}}
}

// Create{{_pascalCase table.name}} response
message Create{{_pascalCase table.name}}Response {
  // created {{_pascalCase table.name}} id
  int64 id = 1;
}

// Update{{_pascalCase table.name}} request
message Update{{_pascalCase table.name}}Request {
{{#each table.columns}}
  {{#if isPrimaryKey}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{else}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  optional {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/if}}
{{/each}}
}

// Update{{_pascalCase table.name}} response
message Update{{_pascalCase table.name}}Response {
  // update success?
  bool success = 1;
}

// Delete{{_pascalCase table.name}} request
message Delete{{_pascalCase table.name}}Request {
  // id
  int64 id = 1;
}

// Delete{{_pascalCase table.name}} response
message Delete{{_pascalCase table.name}}Response {
  // delete success?
  bool success = 1;
}
