Handlebars.registerHelper('_hasDate', function (table) {
  return table.columns.some(e => /date/.test(e.type));
});

Handlebars.registerHelper('_hasTimestamp', function (table) {
  return table.columns.some(e => /datetime|timestamp/.test(e.type));
});

Handlebars.registerHelper('_goPkg', function (pkg) {
  // igigdb.router.v1 -> igigdb/router/v1;router
  const arr = pkg.split('.');
  const goPackage = arr[1];
  return arr.join('/') + ';' + goPackage;
});

Handlebars.registerHelper('_javaPkg', function (pkg) {
  return pkg
});
