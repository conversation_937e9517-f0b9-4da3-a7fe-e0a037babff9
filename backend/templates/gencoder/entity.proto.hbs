// @gencoder.generated: {{properties.baseDir}}/{{_replaceAll properties.protoPackage '.' '/'}}/{{_snakeCase table.name}}.proto

syntax = "proto3";

package {{properties.protoPackage}};

option go_package = "{{_goPkg properties.protoPackage}}";
option java_multiple_files = true;
option java_package = "{{_javaPkg properties.protoPackage}}";

// table: {{table.name}}
// comment: {{table.comment}}
// indexes:
     {{#each table.indexes}}
//   {{name}}: ({{#each columns}}{{name}}{{#unless @last}}, {{/unless}}{{/each}})
     {{/each}}
message {{_pascalCase table.name}}Model {
  {{#each table.columns}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  {{#if isNullable}}optional {{/if}}{{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/each}}
}
