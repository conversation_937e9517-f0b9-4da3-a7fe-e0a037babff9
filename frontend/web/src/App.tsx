import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import HomePage from "@/pages/home";
import PerformancesPage from "@/pages/performances";
import ArtistsPage from "@/pages/artists";
import ArtistDetailPage from "@/pages/artist-detail";
import LoginPage from "@/pages/login";
import SignupPage from "@/pages/signup";
import ForgotPasswordPage from "@/pages/forgot-password";
import PerformanceDetailPage from "@/pages/performance-detail";
import UserProfilePage from "@/pages/user-profile";

function App() {
  return (
    <Router>
      <div className="dark flex min-h-screen flex-col font-sans antialiased">
        <Header />
        <main className="w-full flex-1">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/performances" element={<PerformancesPage />} />
            <Route
              path="/performances/:id"
              element={<PerformanceDetailPage />}
            />
            <Route path="/artists" element={<ArtistsPage />} />
            <Route path="/artists/:id" element={<ArtistDetailPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignupPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/profile" element={<UserProfilePage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
