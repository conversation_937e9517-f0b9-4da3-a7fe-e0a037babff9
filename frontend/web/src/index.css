/* Google Fonts Import */
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 0.75rem;
    --background: 0.98 0.01 265;
    --foreground: 0.25 0.01 265;
    --card: 1 0.01 265;
    --card-foreground: 0.25 0.01 265;
    --popover: 1 0.01 265;
    --popover-foreground: 0.25 0.01 265;
    --primary: 0.45 0.08 245;
    --primary-foreground: 0.98 0.01 265;
    --secondary: 0.95 0.01 265;
    --secondary-foreground: 0.35 0.01 265;
    --muted: 0.96 0.01 265;
    --muted-foreground: 0.55 0.01 265;
    --accent: 0.65 0.1 200; /* Brand accent color - modern blue */
    --star: 0.75 0.15 85; /* Star rating color - soft amber */
    --accent-foreground: 0.98 0.01 265;
    --destructive: 0.6 0.1 30;
    --destructive-foreground: 0.98 0.01 265;
    --border: 0.9 0.01 265;
    --input: 0.9 0.01 265;
    --ring: 0.5 0.05 245;
  }

  .dark {
    --background: 0.12 0.02 265;
    --foreground: 0.95 0.01 265;
    --card: 0.15 0.02 265;
    --card-foreground: 0.95 0.01 265;
    --popover: 0.15 0.02 265;
    --popover-foreground: 0.95 0.01 265;
    --primary: 0.6 0.1 245;
    --primary-foreground: 0.12 0.02 265;
    --secondary: 0.18 0.02 265;
    --secondary-foreground: 0.95 0.01 265;
    --muted: 0.18 0.02 265;
    --muted-foreground: 0.65 0.01 265;
    --accent: 0.65 0.15 200; /* Brand accent color - modern blue */
    --star: 0.75 0.15 85; /* Star rating color - soft amber */
    --accent-foreground: 0.12 0.02 265;
    --destructive: 0.6 0.15 30;
    --destructive-foreground: 0.95 0.01 265;
    --border: 0.25 0.02 265;
    --input: 0.25 0.02 265;
    --ring: 0.6 0.1 245;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-medium tracking-tight;
  }
}

/* Custom Font Variables */
:root {
  --font-sans:
    "Plus Jakarta Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --font-heading:
    "Outfit", "Plus Jakarta Sans", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Additional Typography Styles */
.text-balance {
  text-wrap: balance;
}

.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(
    to right,
    oklch(var(--primary)),
    oklch(var(--accent))
  );
  display: inline-block;
}

.font-heading {
  letter-spacing: -0.025em;
}

/* 新增渐变效果 */
.bg-gradient-soft {
  @apply bg-gradient-to-br from-background to-muted;
}

.bg-gradient-card {
  @apply bg-gradient-to-br from-card to-background;
}

.bg-gradient-accent {
  @apply bg-gradient-to-r from-primary via-primary to-accent;
}

/* 新增阴影效果 */
.shadow-soft {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.04),
    0 2px 8px rgba(0, 0, 0, 0.02);
}

.dark .shadow-soft {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow-accent {
  box-shadow: 0 4px 20px rgba(var(--accent), 0.15);
}

.dark .shadow-accent {
  box-shadow: 0 4px 20px rgba(var(--accent), 0.25);
}

/* 自定义动画 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-soft {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

/* Glass morphism */
.glass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.01);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Modern button effects */
[data-variant="gradient"] {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

[data-variant="gradient"]::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
}

[data-variant="gradient"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary), 0.2);
}

[data-variant="gradient"]:hover::before {
  left: 100%;
}

/* 按钮悬停效果 */
.btn-hover-translate {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.btn-hover-translate:hover {
  transform: translateY(-2px);
}
