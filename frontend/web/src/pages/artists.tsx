import { useState, useEffect } from "react";
import { Search, Filter, Music, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArtistCard } from "@/components/artist-card";
import { popularArtists } from "@/data/mock-data";

// 扩展模拟数据，添加更多艺人
interface ExtendedArtist {
  id: string;
  name: string;
  genre: string;
  imageUrl: string;
  rating: number;
  performanceCount: number;
}

// 将 popularArtists 转换为我们需要的格式
const convertedPopularArtists: ExtendedArtist[] = popularArtists.map(
  (artist) => ({
    id: artist.id,
    name: artist.name,
    genre: artist.genres[0], // 使用第一个流派作为 genre
    imageUrl: artist.imageUrl,
    rating: artist.rating,
    performanceCount: Math.floor(Math.random() * 50) + 10, // 随机生成演出数量
  }),
);

const allArtists: ExtendedArtist[] = [
  ...convertedPopularArtists,
  {
    id: "5",
    name: "周杰伦",
    genre: "流行",
    imageUrl:
      "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2070",
    rating: 4.9,
    performanceCount: 210,
  },
  {
    id: "6",
    name: "陈奕迅",
    genre: "流行",
    imageUrl:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?q=80&w=2070",
    rating: 4.8,
    performanceCount: 180,
  },
  {
    id: "7",
    name: "张惠妹",
    genre: "流行",
    imageUrl:
      "https://images.unsplash.com/photo-1605722243979-fe0be8cbb4b9?q=80&w=2070",
    rating: 4.7,
    performanceCount: 150,
  },
  {
    id: "8",
    name: "五月天",
    genre: "摇滚",
    imageUrl:
      "https://images.unsplash.com/photo-1501386761578-eac5c94b800a?q=80&w=2070",
    rating: 4.9,
    performanceCount: 220,
  },
  {
    id: "9",
    name: "Beyond",
    genre: "摇滚",
    imageUrl:
      "https://images.unsplash.com/photo-1598387993281-cecf8b71a8f8?q=80&w=2076",
    rating: 4.8,
    performanceCount: 120,
  },
  {
    id: "10",
    name: "李荣浩",
    genre: "流行",
    imageUrl:
      "https://images.unsplash.com/photo-1549213783-8284d0336c4f?q=80&w=2070",
    rating: 4.6,
    performanceCount: 95,
  },
  {
    id: "11",
    name: "朴树",
    genre: "民谣",
    imageUrl:
      "https://images.unsplash.com/photo-1511367461989-f85a21fda167?q=80&w=2031",
    rating: 4.7,
    performanceCount: 85,
  },
  {
    id: "12",
    name: "张学友",
    genre: "流行",
    imageUrl:
      "https://images.unsplash.com/photo-1471478331149-c72f17e33c73?q=80&w=2069",
    rating: 4.9,
    performanceCount: 230,
  },
];

// 音乐类型
const genres = [
  "全部",
  "摇滚",
  "电子",
  "古典",
  "爵士",
  "流行",
  "嘻哈",
  "民谣",
  "合成器浪潮",
  "另类摇滚",
];

export default function ArtistsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGenre, setSelectedGenre] = useState("全部");
  const [minRating, setMinRating] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState("rating"); // 'rating' or 'performances'
  const [artists, setArtists] = useState<ExtendedArtist[]>([]);
  const itemsPerPage = 8;

  useEffect(() => {
    // 模拟加载数据
    const timer = setTimeout(() => {
      setArtists(allArtists);
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  // 重置筛选条件
  const resetFilters = () => {
    setSearchQuery("");
    setSelectedGenre("全部");
    setMinRating(0);
    setCurrentPage(1);
  };

  // 筛选艺人
  const filteredArtists = artists.filter((artist: ExtendedArtist) => {
    // 搜索查询
    const matchesSearch =
      searchQuery === "" ||
      artist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      artist.genre?.toLowerCase().includes(searchQuery.toLowerCase());

    // 音乐类型筛选
    const matchesGenre =
      selectedGenre === "全部" ||
      artist.genre?.toLowerCase() === selectedGenre.toLowerCase();

    // 评分筛选
    const matchesRating = artist.rating >= minRating;

    return matchesSearch && matchesGenre && matchesRating;
  });

  // 排序艺人
  const sortedArtists = [...filteredArtists].sort(
    (a: ExtendedArtist, b: ExtendedArtist) => {
      if (sortBy === "rating") {
        return b.rating - a.rating;
      } else {
        return b.performanceCount - a.performanceCount;
      }
    },
  );

  // 计算分页
  const totalPages = Math.ceil(sortedArtists.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedArtists = sortedArtists.slice(
    startIndex,
    startIndex + itemsPerPage,
  );

  // 分页处理函数
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">音乐艺人</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            发现和了解您喜爱的音乐人和乐队，探索他们的精彩演出
          </p>
        </div>

        {/* Search and Filters Bar */}
        <div className="mb-8 bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-lg">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="搜索艺人或音乐类型..."
                className="pl-10 pr-10 h-11 bg-background/50 border-border/50 focus:border-accent focus:ring-accent/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <button
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filter Toggle and Sort */}
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground font-medium">
                共 {filteredArtists.length} 位艺人
              </div>
              <select
                className="rounded-lg border border-border/50 bg-background/50 px-3 py-2 text-sm focus:border-accent focus:ring-accent/20 transition-all"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="rating">按评分排序</option>
                <option value="performances">按演出数量排序</option>
              </select>
              <Button
                variant="outline"
                className="flex items-center gap-2 bg-background/50 hover:bg-accent/10"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4" />
                筛选
                {(selectedGenre !== "全部" || minRating > 0) && (
                  <span className="flex h-5 w-5 items-center justify-center rounded-full bg-accent text-xs text-accent-foreground font-semibold">
                    {(selectedGenre !== "全部" ? 1 : 0) +
                      (minRating > 0 ? 1 : 0)}
                  </span>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mb-6 rounded-lg border bg-card p-6 shadow-sm">
            <div className="mb-6 flex items-center justify-between">
              <h3 className="text-lg font-semibold">筛选条件</h3>
              <Button variant="ghost" size="sm" onClick={resetFilters}>
                重置全部
              </Button>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {/* 音乐类型筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">
                  音乐类型
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  value={selectedGenre}
                  onChange={(e) => setSelectedGenre(e.target.value)}
                >
                  {genres.map((genre) => (
                    <option key={genre} value={genre}>
                      {genre}
                    </option>
                  ))}
                </select>
              </div>

              {/* 最低评分筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">
                  最低评分: {minRating}
                </label>
                <input
                  type="range"
                  min="0"
                  max="5"
                  step="0.5"
                  value={minRating}
                  onChange={(e) => setMinRating(parseFloat(e.target.value))}
                  className="w-full accent-primary"
                />
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {isLoading ? (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="aspect-square rounded-lg bg-muted"></div>
                <div className="mt-3 space-y-2">
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                  <div className="h-3 w-1/2 rounded bg-muted"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
            {paginatedArtists.length > 0 ? (
              paginatedArtists.map((artist: ExtendedArtist) => (
                <ArtistCard key={artist.id} {...artist} />
              ))
            ) : (
              <div className="col-span-full py-16 text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <Music className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">
                  没有找到符合条件的艺人
                </h3>
                <p className="mb-6 text-muted-foreground">
                  尝试调整筛选条件或搜索其他关键词
                </p>
                <Button onClick={resetFilters}>重置筛选条件</Button>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                上一页
              </Button>

              {/* Show page numbers with ellipsis for large page counts */}
              {totalPages <= 7 ? (
                // Show all pages if 7 or fewer
                Array.from({ length: totalPages }).map((_, index) => (
                  <Button
                    key={index}
                    variant={currentPage === index + 1 ? "default" : "outline"}
                    onClick={() => handlePageChange(index + 1)}
                    className="w-10"
                  >
                    {index + 1}
                  </Button>
                ))
              ) : (
                // Show ellipsis for more than 7 pages
                <>
                  <Button
                    variant={currentPage === 1 ? "default" : "outline"}
                    onClick={() => handlePageChange(1)}
                    className="w-10"
                  >
                    1
                  </Button>
                  {currentPage > 3 && <span className="px-2">...</span>}
                  {Array.from({ length: 3 }).map((_, index) => {
                    const pageNum = Math.max(
                      2,
                      Math.min(totalPages - 1, currentPage - 1 + index),
                    );
                    if (pageNum === 1 || pageNum === totalPages) return null;
                    return (
                      <Button
                        key={pageNum}
                        variant={
                          currentPage === pageNum ? "default" : "outline"
                        }
                        onClick={() => handlePageChange(pageNum)}
                        className="w-10"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  {currentPage < totalPages - 2 && (
                    <span className="px-2">...</span>
                  )}
                  <Button
                    variant={currentPage === totalPages ? "default" : "outline"}
                    onClick={() => handlePageChange(totalPages)}
                    className="w-10"
                  >
                    {totalPages}
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
