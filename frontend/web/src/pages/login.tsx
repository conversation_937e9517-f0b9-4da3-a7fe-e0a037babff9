import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { LogIn } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { AuthLayout } from "@/components/auth-layout";
import { AuthInput, PasswordInput } from "@/components/ui/auth-input";
import { SocialButton } from "@/components/ui/social-button";
import { WechatIcon } from "@/components/icons/wechat-icon";

export default function LoginPage() {
  const [emailOrPhone, setEmailOrPhone] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 简单的表单验证
    if (!emailOrPhone || !password) {
      setError("请填写所有必填字段");
      return;
    }

    // 模拟登录过程
    setIsLoading(true);
    setError("");

    try {
      // 这里应该是实际的登录 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // 模拟成功登录
      console.log("登录成功", { emailOrPhone, password });
      navigate("/");
    } catch (err) {
      // 模拟登录失败
      setError("邮箱或密码不正确");
    } finally {
      setIsLoading(false);
    }
  };

  const [wechatLoading, setWechatLoading] = useState(false);

  const handleWechatLogin = () => {
    setWechatLoading(true);
    // 模拟微信登录过程
    setTimeout(() => {
      console.log("微信登录点击");
      setWechatLoading(false);
    }, 1500);
  };

  return (
    <AuthLayout
      backgroundImage="https://images.unsplash.com/photo-1501386761578-eac5c94b800a?q=80&w=2070"
      title="欢迎回来"
      subtitle="登录您的 IGigDb 账户"
    >
      <motion.form
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        onSubmit={handleSubmit}
        className="space-y-6"
      >
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-destructive/15 text-destructive rounded-md p-3 text-sm dark:bg-destructive/25 dark:text-destructive-foreground"
          >
            {error}
          </motion.div>
        )}

        <div className="space-y-2">
          <label
            htmlFor="email"
            className="text-sm font-medium text-white/90 dark:text-white/80"
          >
            邮箱/手机号
          </label>
          <AuthInput
            id="email"
            type="text"
            placeholder="<EMAIL> 或 13800138000"
            value={emailOrPhone}
            onChange={(e) => setEmailOrPhone(e.target.value)}
            required
            autoComplete="username"
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label
              htmlFor="password"
              className="text-sm font-medium text-white/90 dark:text-white/80"
            >
              密码
            </label>
            <Link
              to="/forgot-password"
              className="text-accent hover:text-accent/90 text-xs transition-colors"
            >
              忘记密码?
            </Link>
          </div>
          <PasswordInput
            id="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            autoComplete="current-password"
            showPassword={showPassword}
            onToggleShowPassword={() => setShowPassword(!showPassword)}
          />
        </div>

        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button
            type="submit"
            className="w-full"
            variant="gradient"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                <span>登录中...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <LogIn className="h-4 w-4" />
                <span>登录</span>
              </div>
            )}
          </Button>
        </motion.div>

        <div className="relative my-6 flex items-center justify-center">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-white/10 dark:border-white/5"></span>
          </div>
          <div className="relative bg-card/50 px-4 text-xs text-white/50 dark:bg-card/30 dark:text-white/40">
            或者
          </div>
        </div>

        <SocialButton
          type="button"
          provider="wechat"
          icon={<WechatIcon className="h-5 w-5" />}
          onClick={handleWechatLogin}
          isLoading={wechatLoading}
        >
          微信登录
        </SocialButton>

        <div className="mt-6 text-center text-sm text-white/70 dark:text-white/60">
          还没有账号?{" "}
          <Link
            to="/signup"
            className="text-accent hover:text-accent/90 transition-colors"
          >
            立即注册
          </Link>
        </div>
      </motion.form>
    </AuthLayout>
  );
}
