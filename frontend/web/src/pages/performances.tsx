import { useState, useEffect, useRef } from "react";
import { Search, Filter, Music, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PerformanceCard } from "@/components/performance-card";
import { featuredPerformances } from "@/data/mock-data";

// 扩展模拟数据，添加更多演出
const allPerformances = [
  ...featuredPerformances,
  {
    id: "5",
    title: "冬季音乐会",
    artist: "交响乐团",
    venue: "国家大剧院",
    location: "北京",
    date: "2023-12-15T19:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074",
    rating: 4.6,
    reviewCount: 78,
  },
  {
    id: "6",
    title: "爵士之夜",
    artist: "Blue Note",
    venue: "JZ俱乐部",
    location: "上海",
    date: "2023-11-05T20:30:00",
    imageUrl:
      "https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?q=80&w=2070",
    rating: 4.8,
    reviewCount: 112,
  },
  {
    id: "7",
    title: "民谣音乐节",
    artist: "多位艺术家",
    venue: "草莓音乐节",
    location: "成都",
    date: "2023-10-01T14:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1429962714451-bb934ecdc4ec?q=80&w=2070",
    rating: 4.5,
    reviewCount: 203,
  },
  {
    id: "8",
    title: "电子音乐派对",
    artist: "DJ Shadow",
    venue: "SPACE俱乐部",
    location: "深圳",
    date: "2023-09-22T22:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=2070",
    rating: 4.7,
    reviewCount: 156,
  },
  {
    id: "9",
    title: "摇滚现场",
    artist: "新裤子",
    venue: "MAO Livehouse",
    location: "广州",
    date: "2023-08-18T20:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1524368535928-5b5e00ddc76b?q=80&w=2070",
    rating: 4.9,
    reviewCount: 187,
  },
  {
    id: "10",
    title: "古典音乐会",
    artist: "中国爱乐乐团",
    venue: "音乐厅",
    location: "杭州",
    date: "2023-07-30T19:30:00",
    imageUrl:
      "https://images.unsplash.com/photo-1465847899084-d164df4dedc6?q=80&w=2070",
    rating: 4.6,
    reviewCount: 92,
  },
  {
    id: "11",
    title: "嘻哈说唱夜",
    artist: "Higher Brothers",
    venue: "MYST",
    location: "上海",
    date: "2023-06-25T21:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1571751239008-50cad6cb9265?q=80&w=2070",
    rating: 4.4,
    reviewCount: 134,
  },
  {
    id: "12",
    title: "流行音乐演唱会",
    artist: "周杰伦",
    venue: "体育馆",
    location: "台北",
    date: "2023-05-20T19:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?q=80&w=2070",
    rating: 4.9,
    reviewCount: 312,
  },
];

// 城市列表
const cities = ["全部", "北京", "上海", "广州", "深圳", "成都", "杭州", "台北"];

// 音乐类型
const genres = ["全部", "摇滚", "电子", "古典", "爵士", "流行", "嘻哈", "民谣"];

// 时间筛选选项
const timeFilters = [
  { id: "all", label: "全部时间" },
  { id: "today", label: "今天" },
  { id: "tomorrow", label: "明天" },
  { id: "thisWeek", label: "本周" },
  { id: "thisMonth", label: "本月" },
  { id: "nextMonth", label: "下个月" },
];

export default function PerformancesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCity, setSelectedCity] = useState("全部");
  const [selectedGenre, setSelectedGenre] = useState("全部");
  const [selectedTimeFilter, setSelectedTimeFilter] = useState("all");
  const [minRating, setMinRating] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const itemsPerPage = 20;

  // 获取当前日期
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 获取明天日期
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // 获取本周结束日期
  const thisWeekEnd = new Date(today);
  thisWeekEnd.setDate(thisWeekEnd.getDate() + (7 - thisWeekEnd.getDay()));

  // 获取本月结束日期
  const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  // 获取下个月结束日期
  const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0);

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 检查是否按下了 Cmd+K (Mac) 或 Ctrl+K (Windows/Linux)
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault(); // 阻止默认行为
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }

      // 如果搜索框已聚焦，按下 Escape 键取消聚焦
      if (e.key === "Escape" && isSearchFocused) {
        if (searchInputRef.current) {
          searchInputRef.current.blur();
        }
      }
    };

    // 添加事件监听器
    window.addEventListener("keydown", handleKeyDown);

    // 清理函数
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isSearchFocused]);

  useEffect(() => {
    // 模拟加载数据
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  // 重置筛选条件
  const resetFilters = () => {
    setSearchQuery("");
    setSelectedCity("全部");
    setSelectedGenre("全部");
    setSelectedTimeFilter("all");
    setMinRating(0);
    setCurrentPage(1);
  };

  // 筛选演出
  const filteredPerformances = allPerformances.filter((performance) => {
    // 搜索查询
    const matchesSearch =
      searchQuery === "" ||
      performance.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      performance.artist.toLowerCase().includes(searchQuery.toLowerCase()) ||
      performance.venue.toLowerCase().includes(searchQuery.toLowerCase());

    // 城市筛选
    const matchesCity =
      selectedCity === "全部" || performance.location === selectedCity;

    // 评分筛选
    const matchesRating = performance.rating >= minRating;

    // 音乐类型筛选 (模拟，实际应该在数据中添加类型字段)
    const matchesGenre = selectedGenre === "全部";

    // 时间筛选
    const performanceDate = new Date(performance.date);
    performanceDate.setHours(0, 0, 0, 0);

    let matchesTime = true;
    switch (selectedTimeFilter) {
      case "today":
        matchesTime = performanceDate.getTime() === today.getTime();
        break;
      case "tomorrow":
        matchesTime = performanceDate.getTime() === tomorrow.getTime();
        break;
      case "thisWeek":
        matchesTime =
          performanceDate >= today && performanceDate <= thisWeekEnd;
        break;
      case "thisMonth":
        matchesTime =
          performanceDate >= today && performanceDate <= thisMonthEnd;
        break;
      case "nextMonth":
        matchesTime =
          performanceDate > thisMonthEnd && performanceDate <= nextMonthEnd;
        break;
      default: // 'all'
        matchesTime = true;
    }

    return (
      matchesSearch &&
      matchesCity &&
      matchesRating &&
      matchesGenre &&
      matchesTime
    );
  });

  // 排序演出（按日期升序，最近的演出排在前面）
  const sortedPerformances = [...filteredPerformances].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  );

  // 计算分页
  const totalPages = Math.ceil(sortedPerformances.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedPerformances = sortedPerformances.slice(
    startIndex,
    startIndex + itemsPerPage,
  );

  // 分页处理函数
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">精彩演出</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            发现全球最精彩的现场演出，查看真实观众评价，找到属于你的音乐时刻
          </p>
        </div>

        {/* Search and Filters Bar */}
        <div className="mb-8 bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-lg">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                placeholder="搜索演出、艺人或场地..."
                className="pl-10 pr-10 h-11 bg-background/50 border-border/50 focus:border-accent focus:ring-accent/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
              />
              {searchQuery && (
                <button
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filter Toggle */}
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground font-medium">
                共 {filteredPerformances.length} 场演出
              </div>
              <Button
                variant="outline"
                className="flex items-center gap-2 bg-background/50 hover:bg-accent/10"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4" />
                筛选
                {(selectedCity !== "全部" ||
                  selectedGenre !== "全部" ||
                  minRating > 0 ||
                  selectedTimeFilter !== "all") && (
                  <span className="flex h-5 w-5 items-center justify-center rounded-full bg-accent text-xs text-accent-foreground font-semibold">
                    {(selectedCity !== "全部" ? 1 : 0) +
                      (selectedGenre !== "全部" ? 1 : 0) +
                      (minRating > 0 ? 1 : 0) +
                      (selectedTimeFilter !== "all" ? 1 : 0)}
                  </span>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mb-6 rounded-lg border bg-card p-6 shadow-sm">
            <div className="mb-6 flex items-center justify-between">
              <h3 className="text-lg font-semibold">筛选条件</h3>
              <Button variant="ghost" size="sm" onClick={resetFilters}>
                重置全部
              </Button>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {/* 城市筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">城市</label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                >
                  {cities.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
              </div>

              {/* 音乐类型筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">
                  音乐类型
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  value={selectedGenre}
                  onChange={(e) => setSelectedGenre(e.target.value)}
                >
                  {genres.map((genre) => (
                    <option key={genre} value={genre}>
                      {genre}
                    </option>
                  ))}
                </select>
              </div>

              {/* 最低评分筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">
                  最低评分: {minRating}
                </label>
                <input
                  type="range"
                  min="0"
                  max="5"
                  step="0.5"
                  value={minRating}
                  onChange={(e) => setMinRating(parseFloat(e.target.value))}
                  className="w-full accent-primary"
                />
              </div>

              {/* 时间筛选 */}
              <div>
                <label className="mb-3 block text-sm font-medium">时间</label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  value={selectedTimeFilter}
                  onChange={(e) => setSelectedTimeFilter(e.target.value)}
                >
                  {timeFilters.map((filter) => (
                    <option key={filter.id} value={filter.id}>
                      {filter.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {isLoading ? (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="aspect-[2/3] rounded-lg bg-muted"></div>
                <div className="mt-3 space-y-2">
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                  <div className="h-3 w-1/2 rounded bg-muted"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
            {paginatedPerformances.length > 0 ? (
              paginatedPerformances.map((performance) => (
                <PerformanceCard
                  key={performance.id}
                  performance={performance}
                  compact
                />
              ))
            ) : (
              <div className="col-span-full py-16 text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <Music className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">
                  没有找到符合条件的演出
                </h3>
                <p className="mb-6 text-muted-foreground">
                  尝试调整筛选条件或搜索其他关键词
                </p>
                <Button onClick={resetFilters}>重置筛选条件</Button>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                上一页
              </Button>

              {/* Show page numbers with ellipsis for large page counts */}
              {totalPages <= 7 ? (
                // Show all pages if 7 or fewer
                Array.from({ length: totalPages }).map((_, index) => (
                  <Button
                    key={index}
                    variant={currentPage === index + 1 ? "default" : "outline"}
                    onClick={() => handlePageChange(index + 1)}
                    className="w-10"
                  >
                    {index + 1}
                  </Button>
                ))
              ) : (
                // Show ellipsis for more than 7 pages
                <>
                  <Button
                    variant={currentPage === 1 ? "default" : "outline"}
                    onClick={() => handlePageChange(1)}
                    className="w-10"
                  >
                    1
                  </Button>
                  {currentPage > 3 && <span className="px-2">...</span>}
                  {Array.from({ length: 3 }).map((_, index) => {
                    const pageNum = Math.max(
                      2,
                      Math.min(totalPages - 1, currentPage - 1 + index),
                    );
                    if (pageNum === 1 || pageNum === totalPages) return null;
                    return (
                      <Button
                        key={pageNum}
                        variant={
                          currentPage === pageNum ? "default" : "outline"
                        }
                        onClick={() => handlePageChange(pageNum)}
                        className="w-10"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  {currentPage < totalPages - 2 && (
                    <span className="px-2">...</span>
                  )}
                  <Button
                    variant={currentPage === totalPages ? "default" : "outline"}
                    onClick={() => handlePageChange(totalPages)}
                    className="w-10"
                  >
                    {totalPages}
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
