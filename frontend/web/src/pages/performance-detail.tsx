import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Calendar,
  Clock,
  MapPin,
  Star,
  Play,
  Share2,
  Heart,
  Bookmark,
  Users,
  Music,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatDate } from "@/lib/utils";
import { featuredPerformances } from "@/data/mock-data";
import { Performance } from "@/types";

interface Review {
  id: string;
  user: {
    id: string;
    name: string;
    avatar: string;
  };
  rating: number;
  date: string;
  content: string;
}

// 模拟评论数据
const mockReviews: Review[] = [
  {
    id: "1",
    user: {
      id: "101",
      name: "张三",
      avatar: "https://i.pravatar.cc/150?img=1",
    },
    rating: 4.5,
    date: "2023-08-15T14:30:00",
    content:
      "这场演出真的太棒了！音响效果一流，歌手的现场表现超出预期。特别是中间的那段即兴演奏，简直让人陶醉。",
  },
  {
    id: "2",
    user: {
      id: "102",
      name: "李四",
      avatar: "https://i.pravatar.cc/150?img=2",
    },
    rating: 5,
    date: "2023-08-14T19:45:00",
    content: "无与伦比的现场体验！每一首歌都充满能量，灯光效果也很震撼。",
  },
  {
    id: "3",
    user: {
      id: "103",
      name: "王五",
      avatar: "https://i.pravatar.cc/150?img=3",
    },
    rating: 3.5,
    date: "2023-08-13T22:10:00",
    content: "演出本身很精彩，但场地组织有些混乱。入场排队时间太长。",
  },
];

// 模拟演出详情数据
const mockPerformanceDetails = {
  description:
    "这是一场不容错过的音乐盛宴！艺术家将带来他们最新专辑的现场表演，以及经典曲目的重新演绎。现场乐队阵容强大，视听效果震撼，保证给您带来难忘的音乐体验。",
  genres: ["流行", "电子"],
  duration: "120分钟",
  price: "¥180-580",
  status: "在售",
};

export default function PerformanceDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [performance, setPerformance] = useState<Performance | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPerformance = async () => {
      setIsLoading(true);
      try {
        await new Promise((resolve) => setTimeout(resolve, 500));

        const foundPerformance = featuredPerformances.find((p) => p.id === id);

        if (foundPerformance) {
          setPerformance({
            ...foundPerformance,
            ...mockPerformanceDetails,
          } as Performance);
        }
      } catch (error) {
        console.error("Error fetching performance:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchPerformance();
    }
  }, [id]);

  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 w-1/3 bg-muted rounded"></div>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="h-96 bg-muted rounded-lg"></div>
            <div className="md:col-span-2 space-y-4">
              <div className="h-8 w-2/3 bg-muted rounded"></div>
              <div className="h-4 w-1/2 bg-muted rounded"></div>
              <div className="h-20 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!performance) {
    return (
      <div className="container flex min-h-[50vh] items-center justify-center">
        <div className="text-center">
          <h2 className="mb-4 text-2xl font-bold">演出未找到</h2>
          <p className="mb-6 text-muted-foreground">
            抱歉，我们找不到您请求的演出信息
          </p>
          <Button asChild>
            <Link to="/performances">返回演出列表</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="mb-8 grid gap-8 md:grid-cols-3">
          {/* Poster */}
          <div className="md:col-span-1">
            <div className="sticky top-8">
              <div className="aspect-[2/3] overflow-hidden rounded-xl shadow-soft bg-card border border-border/50">
                <img
                  src={performance.imageUrl}
                  alt={performance.title}
                  className="h-full w-full object-cover"
                />
              </div>

              {/* Action Buttons */}
              <div className="mt-6 space-y-3">
                <Button className="w-full" size="lg" variant="gradient">
                  <Play className="mr-2 h-4 w-4" />
                  观看预告片
                </Button>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-background/50 hover:bg-accent/10"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-background/50 hover:bg-accent/10"
                  >
                    <Bookmark className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-background/50 hover:bg-accent/10"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="md:col-span-2">
            <div className="space-y-8">
              {/* Title and Rating */}
              <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
                <h1 className="mb-4 text-3xl font-bold md:text-4xl tracking-tight">
                  {performance.title}
                </h1>
                <div className="mb-6 flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Star className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                    <span className="text-2xl font-bold">
                      {performance.rating.toFixed(1)}
                    </span>
                    <span className="text-muted-foreground text-lg">
                      ({performance.reviewCount} 评价)
                    </span>
                  </div>
                </div>

                {/* Genres */}
                <div className="flex flex-wrap gap-2">
                  {performance.genres?.map((genre) => (
                    <Badge
                      key={genre}
                      variant="secondary"
                      className="bg-accent/10 text-accent border-accent/20"
                    >
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Performance Details */}
              <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
                <h3 className="mb-4 text-lg font-semibold">演出信息</h3>
                <div className="grid gap-6 sm:grid-cols-2">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <Music className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          艺人
                        </span>
                        <div className="font-medium">
                          <Link
                            to={`/artists/${performance.artist}`}
                            className="text-accent hover:underline"
                          >
                            {performance.artist}
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <Calendar className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          日期
                        </span>
                        <div className="font-medium">
                          {formatDate(performance.date)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <Clock className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          时长
                        </span>
                        <div className="font-medium">
                          {performance.duration}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <MapPin className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          场地
                        </span>
                        <div className="font-medium">
                          {performance.venue}, {performance.location}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <Users className="h-4 w-4 text-accent" />
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          票价
                        </span>
                        <div className="font-medium">{performance.price}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent/10">
                        <span className="text-xs font-bold text-accent">
                          状
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">
                          状态
                        </span>
                        <div>
                          <Badge
                            variant={
                              performance.status === "在售"
                                ? "default"
                                : "secondary"
                            }
                            className="bg-accent/10 text-accent border-accent/20"
                          >
                            {performance.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
                <h3 className="mb-4 text-lg font-semibold">演出介绍</h3>
                <p className="text-muted-foreground leading-relaxed text-base">
                  {performance.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <Tabs defaultValue="reviews" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="reviews">用户评价</TabsTrigger>
            <TabsTrigger value="details">详细信息</TabsTrigger>
            <TabsTrigger value="photos">照片视频</TabsTrigger>
          </TabsList>

          <TabsContent value="reviews" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">用户评价</h3>
                <Button>
                  <Star className="mr-2 h-4 w-4" />
                  写评价
                </Button>
              </div>

              <div className="space-y-4">
                {mockReviews.map((review) => (
                  <div key={review.id} className="rounded-lg border p-4">
                    <div className="flex items-start gap-4">
                      <img
                        src={review.user.avatar}
                        alt={review.user.name}
                        className="h-10 w-10 rounded-full"
                      />
                      <div className="flex-1">
                        <div className="mb-2 flex items-center justify-between">
                          <div>
                            <div className="font-medium">
                              {review.user.name}
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="ml-1 text-sm font-medium">
                                  {review.rating}
                                </span>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {formatDate(review.date)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <p className="text-muted-foreground">
                          {review.content}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="mt-6">
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">演出详情</h3>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h4 className="mb-2 font-medium">基本信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">演出时长:</span>
                        <span>{performance.duration}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">票价范围:</span>
                        <span>{performance.price}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">售票状态:</span>
                        <Badge
                          variant={
                            performance.status === "在售"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {performance.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="mb-2 font-medium">场地信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">场地名称:</span>
                        <span>{performance.venue}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">所在城市:</span>
                        <span>{performance.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="photos" className="mt-6">
            <div className="space-y-6">
              <h3 className="text-xl font-semibold">照片和视频</h3>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div
                    key={i}
                    className="aspect-video overflow-hidden rounded-lg bg-muted"
                  >
                    <img
                      src={`https://images.unsplash.com/photo-150138676157${i}-eac5c94b800a?q=80&w=400`}
                      alt={`演出照片 ${i}`}
                      className="h-full w-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
