import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Music,
  Calendar,
  // MapPin, // 未使用的导入
  Heart,
  Share2,
  ChevronLeft,
  ExternalLink,
  Play,
  // Disc, // 未使用的导入
  Users,
  Globe,
  Instagram,
  Twitter,
  Youtube,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Rating } from "@/components/ui/rating";
import { PerformanceCard } from "@/components/performance-card";
// import { formatDate } from '@/lib/utils'; // 未使用的导入
import { popularArtists, featuredPerformances } from "@/data/mock-data";

interface ArtistMember {
  name: string;
  role: string;
}

interface ArtistAlbum {
  title: string;
  year: string;
  cover: string;
}

interface Artist {
  id: string;
  name: string;
  imageUrl: string;
  coverUrl: string;
  genres: string[];
  rating: number;
  reviewCount: number;
  bio: string;
  members: ArtistMember[];
  albums: ArtistAlbum[];
  performanceCount?: number; // 添加可选属性
  socialMedia?: {
    // 添加可选属性
    website?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
  };
  socialLinks?: {
    // 原来的属性名称
    website?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
  };
  stats: {
    monthlyListeners: number;
    followers: number;
    performances: number;
    topCities: string[];
  };
}

// 模拟艺人详情数据
const mockArtistDetails = {
  coverUrl:
    "https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?q=80&w=2070",
  bio: "这支乐队成立于2010年，由四位才华横溢的音乐人组成。他们的音乐风格融合了电子、摇滚和流行元素，创造出独特的声音。乐队因其充满活力的现场表演和富有情感的歌词而广受赞誉。\n\n多年来，他们发行了多张广受好评的专辑，并在全球各地举办了无数场演唱会。他们的音乐不仅在商业上取得了成功，也赢得了评论家的赞誉，获得了多个音乐奖项。\n\n乐队成员致力于通过音乐传递积极的信息，他们的作品常常探讨爱、希望和社会议题。",
  members: [
    { name: "张伟", role: "主唱/吉他手" },
    { name: "李明", role: "贝斯手" },
    { name: "王芳", role: "鼓手" },
    { name: "刘强", role: "键盘手" },
  ],
  albums: [
    {
      title: "新时代",
      year: "2022",
      cover:
        "https://images.unsplash.com/photo-1614613535308-eb5fbd3d2c17?q=80&w=2070",
    },
    {
      title: "星空下",
      year: "2019",
      cover:
        "https://images.unsplash.com/photo-1496293455970-f8581aae0e3b?q=80&w=2013",
    },
    {
      title: "初次见面",
      year: "2015",
      cover:
        "https://images.unsplash.com/photo-1629276301820-0f3eedc29fd0?q=80&w=2071",
    },
    {
      title: "出发",
      year: "2012",
      cover:
        "https://images.unsplash.com/photo-1557672172-298e090bd0f1?q=80&w=1974",
    },
  ],
  socialMedia: {
    website: "https://example.com",
    instagram: "https://instagram.com",
    twitter: "https://twitter.com",
    youtube: "https://youtube.com",
  },
  stats: {
    monthlyListeners: 2500000,
    followers: 800000,
    performances: 120,
    topCities: ["北京", "上海", "广州", "成都", "深圳"],
  },
};

export default function ArtistDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [artist, setArtist] = useState<Artist | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("about"); // 'about', 'performances', 'albums'
  const [isFavorite, setIsFavorite] = useState(false);
  const [artistPerformances, setArtistPerformances] = useState<
    typeof featuredPerformances
  >([]);

  useEffect(() => {
    // 模拟API请求
    const fetchArtist = async () => {
      setIsLoading(true);
      try {
        // 在实际应用中，这里会是一个API调用
        await new Promise((resolve) => setTimeout(resolve, 800));

        // 从模拟数据中查找艺人
        const foundArtist = popularArtists.find(
          (a: (typeof popularArtists)[0]) => a.id === id,
        );

        if (foundArtist) {
          // 合并基本数据和详细数据
          setArtist({
            ...foundArtist,
            ...mockArtistDetails,
          });

          // 获取艺人的演出
          const performances = featuredPerformances.filter((p) =>
            p.artist.toLowerCase().includes(foundArtist.name.toLowerCase()),
          );
          setArtistPerformances(performances);
        } else {
          console.error("Artist not found");
        }
      } catch (error) {
        console.error("Error fetching artist:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchArtist();
    }
  }, [id]);

  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="flex flex-col gap-8">
          {/* 骨架屏 */}
          <div className="bg-muted h-96 w-full animate-pulse rounded-xl"></div>
          <div className="space-y-4">
            <div className="bg-muted h-8 w-2/3 animate-pulse rounded"></div>
            <div className="bg-muted h-4 w-1/2 animate-pulse rounded"></div>
            <div className="bg-muted h-4 w-1/3 animate-pulse rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!artist) {
    return (
      <div className="container flex min-h-[50vh] flex-col items-center justify-center py-8">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-bold">艺人未找到</h2>
          <p className="text-muted-foreground mb-6">
            抱歉，我们找不到您请求的艺人信息
          </p>
          <Button asChild>
            <Link to="/artists">返回艺人列表</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen">
      {/* 顶部导航 */}
      <div className="container py-4">
        <Button variant="outline" size="sm" asChild>
          <Link to="/artists">
            <ChevronLeft className="mr-1 h-4 w-4" />
            返回艺人列表
          </Link>
        </Button>
      </div>

      <div className="container px-4">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* 主要内容 */}
          <div className="lg:col-span-2">
            {/* 艺人信息 */}
            <div className="bg-card rounded-xl border p-6 shadow-lg">
              <h1 className="font-heading text-2xl font-bold sm:text-3xl">
                {artist.name}
              </h1>

              <div className="mt-2 flex items-center gap-2">
                <Rating value={artist.rating} readOnly />
                <span className="font-medium">{artist.rating.toFixed(1)}</span>
                <span className="text-muted-foreground text-sm">
                  ({artist.stats.performances} 场演出)
                </span>
              </div>

              <div className="mt-4 flex items-center gap-2">
                <span className="bg-accent/10 text-accent inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium">
                  {artist.genres[0]}
                </span>
              </div>

              <div className="mt-6 flex flex-wrap gap-2">
                <Button
                  variant="gradient"
                  className="flex-1 sm:flex-none"
                  onClick={() => {
                    // 在实际应用中，这里会跳转到艺人的演出列表
                    setActiveTab("performances");
                  }}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  查看演出
                </Button>
                <Button
                  variant="outline"
                  className="flex-1 sm:flex-none"
                  onClick={() => setIsFavorite(!isFavorite)}
                >
                  <Heart
                    className={`mr-2 h-4 w-4 ${isFavorite ? "fill-red-500 text-red-500" : ""}`}
                  />
                  {isFavorite ? "已关注" : "关注"}
                </Button>
                <Button
                  variant="outline"
                  className="flex-1 sm:flex-none"
                  onClick={() => {
                    // 在实际应用中，这里会打开分享对话框
                    alert("分享功能");
                  }}
                >
                  <Share2 className="mr-2 h-4 w-4" />
                  分享
                </Button>
              </div>
            </div>

            {/* 标签页导航 */}
            <div className="mt-6 border-b">
              <div className="flex space-x-6">
                <button
                  className={`border-b-2 pb-2 pt-1 text-sm font-medium ${
                    activeTab === "about"
                      ? "border-accent text-accent"
                      : "text-muted-foreground hover:text-foreground border-transparent"
                  }`}
                  onClick={() => setActiveTab("about")}
                >
                  艺人简介
                </button>
                <button
                  className={`border-b-2 pb-2 pt-1 text-sm font-medium ${
                    activeTab === "performances"
                      ? "border-accent text-accent"
                      : "text-muted-foreground hover:text-foreground border-transparent"
                  }`}
                  onClick={() => setActiveTab("performances")}
                >
                  演出 ({artistPerformances.length})
                </button>
                <button
                  className={`border-b-2 pb-2 pt-1 text-sm font-medium ${
                    activeTab === "albums"
                      ? "border-accent text-accent"
                      : "text-muted-foreground hover:text-foreground border-transparent"
                  }`}
                  onClick={() => setActiveTab("albums")}
                >
                  专辑 ({artist.albums.length})
                </button>
              </div>
            </div>

            {/* 标签页内容 */}
            <div className="mt-6">
              {/* 艺人简介 */}
              {activeTab === "about" && (
                <div className="space-y-6">
                  <div>
                    <h3 className="mb-3 text-lg font-semibold">简介</h3>
                    <div className="text-muted-foreground whitespace-pre-line">
                      {artist.bio}
                    </div>
                  </div>

                  {artist.members && artist.members.length > 0 && (
                    <div>
                      <h3 className="mb-3 text-lg font-semibold">成员</h3>
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        {artist.members.map(
                          (member: ArtistMember, index: number) => (
                            <div
                              key={index}
                              className="flex items-center rounded-lg border p-3"
                            >
                              <div className="bg-accent/10 text-accent mr-3 flex h-10 w-10 items-center justify-center rounded-full">
                                <Users className="h-5 w-5" />
                              </div>
                              <div>
                                <div className="font-medium">{member.name}</div>
                                <div className="text-muted-foreground text-sm">
                                  {member.role}
                                </div>
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 演出 */}
              {activeTab === "performances" && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">即将到来的演出</h3>

                  {artistPerformances.length > 0 ? (
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      {artistPerformances.map((performance) => (
                        <PerformanceCard
                          key={performance.id}
                          performance={performance}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="rounded-lg border p-6 text-center">
                      <Music className="text-muted-foreground mx-auto mb-3 h-8 w-8" />
                      <h4 className="mb-1 text-lg font-medium">暂无演出</h4>
                      <p className="text-muted-foreground">
                        该艺人目前没有即将到来的演出
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* 专辑 */}
              {activeTab === "albums" && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">专辑</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                    {artist.albums.map((album: ArtistAlbum, index: number) => (
                      <div
                        key={index}
                        className="group overflow-hidden rounded-lg border"
                      >
                        <div className="relative aspect-square overflow-hidden">
                          <img
                            src={album.cover}
                            alt={album.title}
                            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-black/30 opacity-0 transition-opacity group-hover:opacity-100">
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-12 w-12 rounded-full border-white/30 bg-black/30 text-white backdrop-blur-sm hover:bg-black/50"
                                onClick={() => {
                                  // 在实际应用中，这里会播放专辑
                                  alert(`播放专辑: ${album.title}`);
                                }}
                              >
                                <Play className="h-6 w-6" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <h4 className="font-medium">{album.title}</h4>
                          <p className="text-muted-foreground text-sm">
                            {album.year}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 社交媒体 */}
            <div className="bg-card rounded-xl border p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-semibold">社交媒体</h3>
              <div className="space-y-3">
                {artist.socialMedia?.website && (
                  <a
                    href={artist.socialMedia?.website || ""}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-accent flex items-center hover:underline"
                  >
                    <Globe className="mr-2 h-4 w-4" />
                    官方网站
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                )}
                {artist.socialMedia?.instagram && (
                  <a
                    href={artist.socialMedia?.instagram || ""}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-accent flex items-center hover:underline"
                  >
                    <Instagram className="mr-2 h-4 w-4" />
                    Instagram
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                )}
                {artist.socialMedia?.twitter && (
                  <a
                    href={artist.socialMedia?.twitter || ""}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-accent flex items-center hover:underline"
                  >
                    <Twitter className="mr-2 h-4 w-4" />
                    Twitter
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                )}
                {artist.socialMedia?.youtube && (
                  <a
                    href={artist.socialMedia?.youtube || ""}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-accent flex items-center hover:underline"
                  >
                    <Youtube className="mr-2 h-4 w-4" />
                    YouTube
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
