import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// Card 组件在此页面中未使用，但保留导入以保持一致性
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 简单的表单验证
    if (!email) {
      setError("请输入您的邮箱或手机号");
      return;
    }

    // 模拟发送重置密码邮件的过程
    setIsLoading(true);
    setError("");

    try {
      // 这里应该是实际的 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // 模拟成功发送邮件
      setIsSubmitted(true);
    } catch (err) {
      // 模拟发送失败
      setError("发送重置密码链接失败，请稍后再试");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative min-h-[calc(100vh-80px)] w-full overflow-hidden">
      {/* 背景图和渐变 */}
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1465847899084-d164df4dedc6?q=80&w=2070"
          alt="音乐会背景"
          className="h-full w-full object-cover brightness-[0.4] contrast-[1.1] saturate-[1.1]"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/40"></div>
      </div>

      {/* 忘记密码表单 */}
      <div className="container relative z-10 flex min-h-[calc(100vh-80px)] items-center justify-center px-4 py-8">
        <div className="w-full max-w-md overflow-hidden rounded-2xl border border-white/10 shadow-2xl">
          <div className="glass bg-white/5 p-8 backdrop-blur-md">
            <div className="mb-6 text-center">
              <h2 className="font-heading text-2xl font-bold text-white">
                重置密码
              </h2>
              <p className="mt-2 text-sm text-white/80">
                {!isSubmitted
                  ? "输入您的邮箱或手机号，我们将发送重置密码的验证码"
                  : "重置密码验证码已发送"}
              </p>
            </div>

            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-destructive/15 text-destructive rounded-md p-3 text-sm">
                    {error}
                  </div>
                )}
                <div className="space-y-2">
                  <label
                    htmlFor="email"
                    className="text-sm font-medium text-white/90"
                  >
                    邮箱/手机号
                  </label>
                  <Input
                    id="email"
                    type="text"
                    placeholder="<EMAIL> 或 13800138000"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    autoComplete="username"
                    className="focus-visible:ring-accent/50 border-white/10 bg-white/10 text-white placeholder:text-white/50"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  variant="gradient"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                      <span>发送中...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Send className="h-4 w-4" />
                      <span>发送验证码</span>
                    </div>
                  )}
                </Button>

                <div className="text-center text-sm text-white/70">
                  <Link
                    to="/login"
                    className="hover:text-accent/90 text-accent inline-flex items-center gap-1"
                  >
                    <ArrowLeft className="h-3 w-3" />
                    返回登录
                  </Link>
                </div>
              </form>
            ) : (
              <div className="space-y-6">
                <div className="rounded-md bg-green-500/15 p-4 text-sm text-green-400">
                  <p>
                    我们已向 <strong>{email}</strong> 发送了重置密码的验证码。
                  </p>
                  <p className="mt-2">
                    请检查您的邮箱或手机短信，并使用验证码重置密码。
                  </p>
                </div>
                <div className="text-center">
                  <Button
                    asChild
                    variant="outline"
                    className="border-white/10 text-white hover:bg-white/10"
                  >
                    <Link
                      to="/login"
                      className="inline-flex items-center gap-2"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      返回登录
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
