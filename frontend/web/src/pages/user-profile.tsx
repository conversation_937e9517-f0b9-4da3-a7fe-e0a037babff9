import { useState } from "react";
import { ProfileLayout } from "@/components/profile-layout";
import { ProfileCard } from "@/components/profile-card";
import { EditProfileForm } from "@/components/edit-profile-form";
import { ProfileTabs } from "@/components/profile-tabs";
import {
  User,
  UserProfileFormData,
  AttendedPerformance,
  Review,
} from "@/types";

// 模拟用户数据
const mockUser: User = {
  id: "user1",
  name: "张三",
  username: "z<PERSON><PERSON>",
  avatar: "https://i.pravatar.cc/150?img=1",
  email: "<EMAIL>",
  location: "北京",
  joinDate: "2023-01-15",
};

// 模拟用户参加过的演出（包含评论）
const mockAttended: AttendedPerformance[] = [
  {
    id: "2",
    title: "世界巡演",
    artist: "Daft Punk",
    venue: "Madison Square Garden",
    location: "广州",
    date: "2023-07-20T21:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?q=80&w=2070",
    rating: 4.9,
    reviewCount: 215,
    userRating: 5.0,
    reviewDate: "2023-07-21T10:15:00",
  },
  {
    id: "4",
    title: "音乐节主演",
    artist: "Radiohead",
    venue: "Coachella",
    location: "深圳",
    date: "2023-08-05T18:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1429962714451-bb934ecdc4ec?q=80&w=2070",
    rating: 4.2,
    reviewCount: 189,
    userRating: 4.0,
    reviewDate: "2023-08-06T09:30:00",
  },
  {
    id: "7",
    title: "电子音乐派对",
    artist: "Avicii Tribute",
    venue: "Space Club",
    location: "深圳",
    date: "2023-08-05T22:00:00",
    imageUrl:
      "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=2070",
    rating: 1.9,
    reviewCount: 92,
    userRating: 2.5,
    reviewDate: "2023-08-06T11:45:00",
  },
];

export default function UserProfilePage() {
  const [activeTab, setActiveTab] = useState("reviews");
  const [isEditing, setIsEditing] = useState(false);
  const [userInfo, setUserInfo] = useState(mockUser);

  // Convert attended performances to reviews (all have reviewDate)
  const mockReviews: Review[] = mockAttended.map((item) => ({
    ...item,
    reviewDate: item.reviewDate!, // We know all items have reviewDate
  }));

  // 处理用户信息更新
  const handleUpdateProfile = (updatedUser: UserProfileFormData) => {
    // 在实际应用中，这里会发送API请求更新用户信息
    setUserInfo(updatedUser);
    setIsEditing(false);
    // 显示成功消息
    alert("个人资料已更新");
  };

  return (
    <ProfileLayout>
      {/* 用户基本信息卡片 */}
      <ProfileCard user={userInfo} onEdit={() => setIsEditing(true)} />

      {/* 编辑个人资料表单 */}
      {isEditing && (
        <EditProfileForm
          user={userInfo}
          onCancel={() => setIsEditing(false)}
          onSave={handleUpdateProfile}
        />
      )}

      {/* 标签页内容 */}
      <ProfileTabs
        reviews={mockReviews}
        attended={mockAttended}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />
    </ProfileLayout>
  );
}
