import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { UserPlus, Check } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

import { AuthLayout } from "@/components/auth-layout";
import { AuthInput, PasswordInput } from "@/components/ui/auth-input";
import { SocialButton } from "@/components/ui/social-button";
import { WechatIcon } from "@/components/icons/wechat-icon";

export default function SignupPage() {
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [agreeTerms, setAgreeTerms] = useState(false);
  const navigate = useNavigate();

  // 密码强度检查
  const getPasswordStrength = (password: string) => {
    if (!password) return { score: 0, text: "" };

    let score = 0;
    if (password.length >= 8) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;

    const strengthTexts = ["", "弱", "中等", "良好", "强", "非常强"];
    return { score, text: strengthTexts[score] };
  };

  const passwordStrength = getPasswordStrength(password);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    if (!username || !email || !password || !confirmPassword) {
      setError("请填写所有必填字段");
      return;
    }

    if (password !== confirmPassword) {
      setError("两次输入的密码不一致");
      return;
    }

    if (passwordStrength.score < 3) {
      setError("请设置更强的密码");
      return;
    }

    if (!agreeTerms) {
      setError("请阅读并同意服务条款和隐私政策");
      return;
    }

    // 模拟注册过程
    setIsLoading(true);
    setError("");

    try {
      // 这里应该是实际的注册 API 调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // 模拟成功注册
      console.log("注册成功", { username, email, password });
      navigate("/login", { state: { registered: true } });
    } catch (err) {
      // 模拟注册失败
      setError("注册失败，请稍后再试");
    } finally {
      setIsLoading(false);
    }
  };

  const [wechatLoading, setWechatLoading] = useState(false);

  const handleWechatSignup = () => {
    setWechatLoading(true);
    // 模拟微信注册过程
    setTimeout(() => {
      console.log("微信注册点击");
      setWechatLoading(false);
    }, 1500);
  };

  return (
    <AuthLayout
      backgroundImage="https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?q=80&w=2070"
      title="加入 IGigDb"
      subtitle="创建账户，发现更多精彩演出"
    >
      <motion.form
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        onSubmit={handleSubmit}
        className="space-y-5"
      >
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-destructive/15 text-destructive rounded-md p-3 text-sm dark:bg-destructive/25 dark:text-destructive-foreground"
          >
            {error}
          </motion.div>
        )}

        <div className="space-y-2">
          <label
            htmlFor="username"
            className="text-sm font-medium text-white/90 dark:text-white/80"
          >
            用户名
          </label>
          <AuthInput
            id="username"
            type="text"
            placeholder="your_username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
            autoComplete="username"
          />
        </div>

        <div className="space-y-2">
          <label
            htmlFor="email"
            className="text-sm font-medium text-white/90 dark:text-white/80"
          >
            邮箱/手机号
          </label>
          <AuthInput
            id="email"
            type="text"
            placeholder="<EMAIL> 或 13800138000"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            autoComplete="username"
          />
        </div>

        <div className="space-y-2">
          <label
            htmlFor="password"
            className="text-sm font-medium text-white/90 dark:text-white/80"
          >
            密码
          </label>
          <PasswordInput
            id="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            autoComplete="new-password"
            showPassword={showPassword}
            onToggleShowPassword={() => setShowPassword(!showPassword)}
          />
          {password && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="mt-2"
            >
              <div className="mb-1 flex justify-between text-xs text-white/90 dark:text-white/80">
                <span>密码强度:</span>
                <span
                  className={
                    passwordStrength.score < 3
                      ? "text-red-400"
                      : passwordStrength.score < 4
                        ? "text-yellow-400"
                        : "text-green-400"
                  }
                >
                  {passwordStrength.text}
                </span>
              </div>
              <div className="h-1 w-full overflow-hidden rounded-full bg-white/10 dark:bg-white/5">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                  transition={{ duration: 0.5 }}
                  className={`h-full transition-colors ${
                    passwordStrength.score < 3
                      ? "bg-red-400"
                      : passwordStrength.score < 4
                        ? "bg-yellow-400"
                        : "bg-green-400"
                  }`}
                ></motion.div>
              </div>
              <ul className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-white/70 dark:text-white/60">
                <li
                  className={`flex items-center gap-1 ${password.length >= 8 ? "text-green-400" : ""}`}
                >
                  {password.length >= 8 && <Check className="h-3 w-3" />}
                  至少8个字符
                </li>
                <li
                  className={`flex items-center gap-1 ${/[A-Z]/.test(password) ? "text-green-400" : ""}`}
                >
                  {/[A-Z]/.test(password) && <Check className="h-3 w-3" />}
                  包含大写字母
                </li>
                <li
                  className={`flex items-center gap-1 ${/[a-z]/.test(password) ? "text-green-400" : ""}`}
                >
                  {/[a-z]/.test(password) && <Check className="h-3 w-3" />}
                  包含小写字母
                </li>
                <li
                  className={`flex items-center gap-1 ${/[0-9]/.test(password) ? "text-green-400" : ""}`}
                >
                  {/[0-9]/.test(password) && <Check className="h-3 w-3" />}
                  包含数字
                </li>
                <li
                  className={`flex items-center gap-1 ${/[^A-Za-z0-9]/.test(password) ? "text-green-400" : ""}`}
                >
                  {/[^A-Za-z0-9]/.test(password) && (
                    <Check className="h-3 w-3" />
                  )}
                  包含特殊字符
                </li>
              </ul>
            </motion.div>
          )}
        </div>

        <div className="space-y-2">
          <label
            htmlFor="confirmPassword"
            className="text-sm font-medium text-white/90 dark:text-white/80"
          >
            确认密码
          </label>
          <PasswordInput
            id="confirmPassword"
            placeholder="••••••••"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            autoComplete="new-password"
            showPassword={showConfirmPassword}
            onToggleShowPassword={() =>
              setShowConfirmPassword(!showConfirmPassword)
            }
          />
          {password && confirmPassword && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className={`mt-1 flex items-center gap-1 text-xs ${password === confirmPassword ? "text-green-400" : "text-red-400"}`}
            >
              {password === confirmPassword ? (
                <>
                  <Check className="h-3 w-3" />
                  密码匹配
                </>
              ) : (
                "密码不匹配"
              )}
            </motion.div>
          )}
        </div>

        <div className="flex items-center gap-2 pt-2">
          <motion.div
            whileTap={{ scale: 0.9 }}
            className={`flex h-5 w-5 cursor-pointer items-center justify-center rounded border transition-colors ${agreeTerms ? "border-accent bg-accent/20" : "border-white/30 bg-white/5 dark:border-white/20 dark:bg-white/5"}`}
            onClick={() => setAgreeTerms(!agreeTerms)}
          >
            {agreeTerms && <Check className="h-3.5 w-3.5 text-accent" />}
          </motion.div>
          <label
            htmlFor="terms"
            className="cursor-pointer text-sm text-white/80 dark:text-white/70"
            onClick={() => setAgreeTerms(!agreeTerms)}
          >
            我同意
            <Link
              to="/terms"
              className="text-accent hover:text-accent/90 transition-colors"
            >
              {" "}
              服务条款
            </Link>{" "}
            和{" "}
            <Link
              to="/privacy"
              className="text-accent hover:text-accent/90 transition-colors"
            >
              隐私政策
            </Link>
          </label>
        </div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="mt-6"
        >
          <Button
            type="submit"
            className="w-full"
            variant="gradient"
            disabled={isLoading || !agreeTerms}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                <span>注册中...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                <span>注册账号</span>
              </div>
            )}
          </Button>
        </motion.div>

        <div className="relative my-6 flex items-center justify-center">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-white/10 dark:border-white/5"></span>
          </div>
          <div className="relative bg-card/50 px-4 text-xs text-white/50 dark:bg-card/30 dark:text-white/40">
            或者
          </div>
        </div>

        <SocialButton
          type="button"
          provider="wechat"
          icon={<WechatIcon className="h-5 w-5" />}
          onClick={handleWechatSignup}
          isLoading={wechatLoading}
        >
          微信注册
        </SocialButton>

        <div className="mt-6 text-center text-sm text-white/70 dark:text-white/60">
          已有账号?{" "}
          <Link
            to="/login"
            className="text-accent hover:text-accent/90 transition-colors"
          >
            立即登录
          </Link>
        </div>
      </motion.form>
    </AuthLayout>
  );
}
