import { <PERSON> } from "react-router-dom";
import { Star, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Carousel } from "@/components/ui/carousel";
import { PerformanceCard } from "@/components/performance-card";
import { featuredPerformances } from "@/data/mock-data";

export default function HomePage() {
  // 获取热门演出（评分最高的前6个）
  const topRatedPerformances = [...featuredPerformances]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 6);

  // 获取最新演出（按日期排序的前6个）
  const recentPerformances = [...featuredPerformances]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 6);

  // 轮播图数据
  const carouselSlides = topRatedPerformances
    .slice(0, 4)
    .map((performance) => ({
      id: performance.id,
      title: performance.title,
      subtitle: `${performance.artist} • ${performance.venue}`,
      imageUrl: performance.imageUrl,
      link: `/performances/${performance.id}`,
    }));

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Carousel Section - IMDb Style */}
      <section className="relative">
        <Carousel slides={carouselSlides} className="mb-8" />
      </section>

      {/* Quick Stats Section */}
      <section className="bg-slate-900 py-12">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="mb-8 text-center">
              <h1 className="mb-4 text-3xl font-bold text-white md:text-4xl">
                IGigDb
              </h1>
              <p className="text-lg text-slate-300">
                互联网现场演出数据库 - 发现、评分、重温每一场难忘演出
              </p>
            </div>

            <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400 md:text-3xl">
                  {featuredPerformances.length}+
                </div>
                <div className="text-sm text-slate-400">演出记录</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400 md:text-3xl">
                  {featuredPerformances.reduce(
                    (sum, p) => sum + p.reviewCount,
                    0,
                  )}
                  +
                </div>
                <div className="text-sm text-slate-400">用户评价</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400 md:text-3xl">
                  100+
                </div>
                <div className="text-sm text-slate-400">艺人档案</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400 md:text-3xl">
                  50+
                </div>
                <div className="text-sm text-slate-400">城市覆盖</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Today Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="mb-12 flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">今日推荐</h2>
              <p className="mt-2 text-lg text-muted-foreground">
                编辑精选的优质演出
              </p>
            </div>
            <Button variant="outline" size="lg" asChild>
              <Link to="/performances">查看全部</Link>
            </Button>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {topRatedPerformances.slice(0, 3).map((performance) => (
              <PerformanceCard key={performance.id} performance={performance} />
            ))}
          </div>
        </div>
      </section>

      {/* Top Rated Section */}
      <section className="py-16 bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="mb-12 flex items-center gap-4">
            <Star className="h-8 w-8 text-yellow-500" />
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-white">
                高分演出
              </h2>
              <p className="mt-2 text-lg text-slate-300">
                观众评分最高的现场演出
              </p>
            </div>
          </div>

          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
            {topRatedPerformances.map((performance) => (
              <PerformanceCard
                key={performance.id}
                performance={performance}
                compact
              />
            ))}
          </div>
        </div>
      </section>

      {/* Recently Added Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="mb-12 flex items-center gap-4">
            <TrendingUp className="h-8 w-8 text-blue-500" />
            <div>
              <h2 className="text-3xl font-bold tracking-tight">最新演出</h2>
              <p className="mt-2 text-lg text-muted-foreground">
                最近添加的演出记录
              </p>
            </div>
          </div>

          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
            {recentPerformances.map((performance) => (
              <PerformanceCard
                key={performance.id}
                performance={performance}
                compact
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
