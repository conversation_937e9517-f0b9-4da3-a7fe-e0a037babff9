// User related types
export interface User {
  id: string;
  name: string;
  username: string;
  avatar: string;
  email?: string;
  location?: string;
  joinDate?: string;
}

// Performance related types
export interface Performance {
  id: string;
  title: string;
  artist: string;
  venue: string;
  location: string;
  date: string;
  imageUrl: string;
  rating: number;
  reviewCount: number;
  // Extended properties for detail page
  description?: string;
  genres?: string[];
  duration?: string;
  price?: string;
  status?: string;
}

// Attended performance with user rating
export interface AttendedPerformance extends Performance {
  userRating: number;
  reviewDate?: string;
  reviewText?: string;
}

// Review data structure - same as AttendedPerformance but with required reviewDate
export interface Review extends Omit<AttendedPerformance, "reviewDate"> {
  reviewDate: string; // Required for reviews
}

// Form data for user profile updates
export interface UserProfileFormData {
  id: string;
  name: string;
  username: string;
  avatar: string;
  email: string;
  location: string;
}
