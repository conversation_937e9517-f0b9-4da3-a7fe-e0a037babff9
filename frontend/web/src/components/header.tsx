import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Search, Menu, X, User, ChevronDown } from "lucide-react";
import { SearchDialog } from "./search-dialog";

export function Header() {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // 模拟登录状态，实际应用中应从认证服务获取
  const [isLoggedIn] = useState(true); // 设置为 true 以模拟已登录状态

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 检查是否按下了 Cmd+K (Mac) 或 Ctrl+K (Windows/Linux)
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault(); // 阻止默认行为
        setIsSearchOpen(true);
      }
    };

    // 添加事件监听器
    window.addEventListener("keydown", handleKeyDown);

    // 清理函数
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur-md">
        <div className="container mx-auto flex h-16 items-center justify-between">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center gap-8">
            <Link to="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-yellow-500">IGigDb</span>
            </Link>

            {/* Main Navigation */}
            <nav className="hidden gap-6 md:flex">
              <div className="group relative">
                <Button variant="ghost" className="flex items-center gap-1">
                  演出
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
              <Link
                to="/artists"
                className="flex items-center px-3 py-2 text-sm font-medium transition-colors hover:text-primary"
              >
                艺人
              </Link>
              <Link
                to="/venues"
                className="flex items-center px-3 py-2 text-sm font-medium transition-colors hover:text-primary"
              >
                场地
              </Link>
            </nav>
          </div>

          {/* Center - Search Bar */}
          <div className="hidden flex-1 max-w-lg mx-8 md:block">
            <div className="relative group">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground group-focus-within:text-accent transition-colors" />
              <Input
                type="text"
                placeholder="搜索演出、艺人、场地... (⌘K)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 h-10 bg-background/50 border-border/50 hover:border-border focus:border-accent focus:ring-accent/20 transition-all duration-200"
                onFocus={() => setIsSearchOpen(true)}
                readOnly
              />
            </div>
          </div>

          {/* Right side - User actions */}
          <div className="flex items-center gap-3">
            {/* Mobile search button */}
            <Button
              variant="ghost"
              size="icon"
              aria-label="搜索"
              onClick={() => setIsSearchOpen(true)}
              className="md:hidden"
            >
              <Search className="h-5 w-5" />
            </Button>

            <div className="hidden items-center gap-3 md:flex">
              {isLoggedIn ? (
                <div className="flex items-center gap-2">
                  <Button variant="ghost" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>我的账户</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <>
                  <Button variant="ghost" asChild>
                    <Link to="/login">登录</Link>
                  </Button>
                  <Button asChild>
                    <Link to="/signup">注册</Link>
                  </Button>
                </>
              )}
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleMobileMenu}
              aria-label="菜单"
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="absolute left-0 right-0 top-full border-b bg-background/95 backdrop-blur-md">
              <nav className="container mx-auto flex flex-col gap-4 p-4">
                <Link
                  to="/"
                  className={`px-3 py-2 text-sm font-medium transition-colors hover:text-primary ${
                    location.pathname === "/"
                      ? "text-primary"
                      : "text-foreground"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  首页
                </Link>
                <Link
                  to="/performances"
                  className={`px-3 py-2 text-sm font-medium transition-colors hover:text-primary ${
                    location.pathname === "/performances"
                      ? "text-primary"
                      : "text-foreground"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  演出
                </Link>
                <Link
                  to="/artists"
                  className="px-3 py-2 text-sm font-medium transition-colors hover:text-primary"
                  onClick={toggleMobileMenu}
                >
                  艺人
                </Link>
                <Link
                  to="/venues"
                  className="px-3 py-2 text-sm font-medium transition-colors hover:text-primary"
                  onClick={toggleMobileMenu}
                >
                  场地
                </Link>

                <div className="mt-4 flex flex-col gap-2 border-t pt-4">
                  {isLoggedIn ? (
                    <Button variant="outline" className="w-full">
                      我的账户
                    </Button>
                  ) : (
                    <>
                      <Button asChild variant="outline" className="w-full">
                        <Link to="/login" onClick={toggleMobileMenu}>
                          登录
                        </Link>
                      </Button>
                      <Button asChild className="w-full">
                        <Link to="/signup" onClick={toggleMobileMenu}>
                          注册
                        </Link>
                      </Button>
                    </>
                  )}
                </div>
              </nav>
            </div>
          </div>
        )}
      </header>

      {/* 搜索对话框 */}
      <SearchDialog
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </>
  );
}
