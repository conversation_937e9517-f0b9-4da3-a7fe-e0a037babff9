import React from "react";
import { motion } from "framer-motion";

interface AuthLayoutProps {
  children: React.ReactNode;
  backgroundImage: string;
  title: string;
  subtitle: string;
}

export function AuthLayout({
  children,
  backgroundImage,
  title,
  subtitle,
}: AuthLayoutProps) {
  return (
    <div className="relative min-h-[calc(100vh-80px)] w-full overflow-hidden">
      {/* 背景图和渐变 */}
      <div className="absolute inset-0 z-0">
        <img
          src={backgroundImage}
          alt="背景图片"
          className="h-full w-full object-cover brightness-[0.7] contrast-[1.1] saturate-[1.05] filter blur-[1px] dark:brightness-[0.8]"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/40 to-black/70 dark:from-black/10 dark:via-black/30 dark:to-black/60"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 mix-blend-overlay opacity-70 dark:opacity-90"></div>
      </div>

      {/* 网格装饰 */}
      <div className="absolute inset-0 z-0 bg-[url('/grid-pattern.svg')] opacity-10"></div>

      {/* 主要内容 */}
      <div className="container relative z-10 flex min-h-[calc(100vh-80px)] items-center justify-center px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md overflow-hidden rounded-xl border border-white/10 shadow-soft dark:shadow-xl"
        >
          <div className="bg-card/80 p-8 backdrop-blur-md dark:bg-card/40">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="mb-6 text-center"
            >
              <h2 className="font-heading text-2xl font-bold text-white dark:text-white">
                {title}
              </h2>
              <p className="mt-1 text-sm text-white/80 dark:text-white/70">
                {subtitle}
              </p>
            </motion.div>
            {children}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
