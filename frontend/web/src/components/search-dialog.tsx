import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { featuredPerformances } from "@/data/mock-data";

interface Performance {
  id: string;
  title: string;
  artist: string;
  venue: string;
  location: string;
  date: string;
  imageUrl: string;
  rating: number;
  reviewCount: number;
}

interface SearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SearchDialog({ isOpen, onClose }: SearchDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Performance[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultRefs = useRef<(HTMLLIElement | null)[]>([]);
  const navigate = useNavigate();

  // 当对话框打开时，聚焦到搜索框
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      // 重置选中的索引
      setSelectedIndex(-1);
    }
  }, [isOpen]);

  // 当搜索结果变化时，重置选中的索引和引用数组
  useEffect(() => {
    setSelectedIndex(-1);
    resultRefs.current = searchResults.map(() => null);
  }, [searchResults]);

  // 处理搜索逻辑
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setSearchResults([]);
      return;
    }

    const query = searchQuery.toLowerCase();
    const results = featuredPerformances.filter(
      (performance) =>
        performance.title.toLowerCase().includes(query) ||
        performance.artist.toLowerCase().includes(query) ||
        performance.venue.toLowerCase().includes(query) ||
        performance.location.toLowerCase().includes(query),
    );

    setSearchResults(results.slice(0, 5)); // 只显示前5个结果
  }, [searchQuery]);

  // 处理结果点击
  const handleResultClick = (id: string) => {
    navigate(`/performances/${id}`);
    onClose();
    setSearchQuery("");
  };

  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 如果没有搜索结果，只处理 Enter 键
    if (searchResults.length === 0) {
      if (e.key === "Enter" && searchQuery.trim() !== "") {
        e.preventDefault();
        navigate(`/performances?search=${encodeURIComponent(searchQuery)}`);
        onClose();
        setSearchQuery("");
      }
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault(); // 防止默认行为
        console.log("Arrow Down pressed"); // 调试日志
        setSelectedIndex((prev) => {
          const newIndex = prev < searchResults.length - 1 ? prev + 1 : 0;
          // 滚动到选中的项
          setTimeout(() => {
            resultRefs.current[newIndex]?.scrollIntoView({ block: "nearest" });
          }, 0);
          return newIndex;
        });
        break;

      case "ArrowUp":
        e.preventDefault(); // 防止默认行为
        console.log("Arrow Up pressed"); // 调试日志
        setSelectedIndex((prev) => {
          const newIndex = prev > 0 ? prev - 1 : searchResults.length - 1;
          // 滚动到选中的项
          setTimeout(() => {
            resultRefs.current[newIndex]?.scrollIntoView({ block: "nearest" });
          }, 0);
          return newIndex;
        });
        break;

      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
          // 如果有选中的项，导航到该项
          handleResultClick(searchResults[selectedIndex].id);
        } else if (searchQuery.trim() !== "") {
          // 如果没有选中的项，但有搜索查询，导航到搜索结果页
          navigate(`/performances?search=${encodeURIComponent(searchQuery)}`);
          onClose();
          setSearchQuery("");
        }
        break;
    }
  };

  // 处理搜索提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() !== "") {
      navigate(`/performances?search=${encodeURIComponent(searchQuery)}`);
      onClose();
      setSearchQuery("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] bg-card/95 backdrop-blur-md border-border/50">
        <div className="space-y-4">
          <div className="text-center">
            <h2 className="text-lg font-semibold">搜索演出</h2>
            <p className="text-sm text-muted-foreground">发现精彩的现场演出</p>
          </div>

          <form onSubmit={handleSubmit} onKeyDown={handleKeyDown}>
            <div className="relative">
              <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
              <Input
                ref={inputRef}
                placeholder="搜索演出、艺人或场地..."
                className="pl-10 h-12 text-base bg-background/50 border-border/50 focus:border-accent focus:ring-accent/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>

          {searchResults.length > 0 && (
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground px-1">
                找到 {searchResults.length} 个结果
              </div>
              <ul className="space-y-1 max-h-64 overflow-y-auto">
                {searchResults.map((result, index) => (
                  <li
                    key={result.id}
                    ref={(el) => {
                      if (el) resultRefs.current[index] = el;
                    }}
                    className={`cursor-pointer rounded-lg p-3 transition-all duration-200 ${
                      index === selectedIndex
                        ? "bg-accent/10 border border-accent/20 shadow-sm"
                        : "hover:bg-muted/50 border border-transparent"
                    }`}
                    onClick={() => handleResultClick(result.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <img
                          src={result.imageUrl}
                          alt={result.title}
                          className="h-12 w-12 rounded-lg object-cover shadow-sm"
                        />
                        <div className="absolute -bottom-1 -right-1 bg-background border border-border rounded-full px-1.5 py-0.5 text-xs font-medium flex items-center gap-0.5">
                          <span className="text-yellow-500">★</span>
                          <span>{result.rating.toFixed(1)}</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold truncate text-foreground">
                          {result.title}
                        </div>
                        <div className="text-sm text-muted-foreground truncate">
                          {result.artist} • {result.venue}
                        </div>
                        <div className="text-xs text-muted-foreground/80 truncate">
                          {result.location}
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
              <div className="pt-2 border-t border-border/50">
                <button
                  className="w-full text-center py-2 px-4 rounded-lg bg-accent/5 hover:bg-accent/10 text-accent font-medium text-sm transition-colors"
                  onClick={() => {
                    navigate(
                      `/performances?search=${encodeURIComponent(searchQuery)}`,
                    );
                    onClose();
                    setSearchQuery("");
                  }}
                >
                  查看全部 {searchResults.length} 个结果
                </button>
              </div>
            </div>
          )}

          <div className="text-muted-foreground/70 mt-4 flex items-center justify-between text-xs border-t border-border/30 pt-3">
            <div className="flex items-center gap-2">
              <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
                ESC
              </kbd>
              <span>关闭</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
                ↑↓
              </kbd>
              <span>选择</span>
              <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
                Enter
              </kbd>
              <span>确认</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
