import { Music } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Rating } from "@/components/ui/rating";

interface ArtistCardProps {
  id: string;
  name: string;
  genre: string;
  imageUrl: string;
  rating: number;
  performanceCount: number;
}

export function ArtistCard({
  id,
  name,
  genre,
  imageUrl,
  rating,
  performanceCount,
}: ArtistCardProps) {
  return (
    <Card className="group overflow-hidden transition-all hover:shadow-md">
      <div className="relative aspect-square overflow-hidden">
        <img
          src={imageUrl}
          alt={name}
          className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 transition-opacity group-hover:opacity-100"></div>
      </div>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="space-y-1">
            <h3 className="line-clamp-1 text-lg font-semibold">
              <Link
                to={`/artists/${id}`}
                className="hover:text-accent transition-colors"
              >
                {name}
              </Link>
            </h3>
            <p className="text-muted-foreground text-sm">{genre}</p>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Rating value={rating} readOnly />
              <span className="text-sm font-medium">{rating.toFixed(1)}</span>
            </div>
            <div className="text-muted-foreground flex items-center gap-1 text-sm">
              <Music className="h-4 w-4" />
              <span>{performanceCount}</span>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button asChild variant="outline" className="w-full">
          <Link to={`/artists/${id}`}>查看详情</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
