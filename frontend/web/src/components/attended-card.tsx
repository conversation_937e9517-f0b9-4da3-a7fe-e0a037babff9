import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";
import { Clock, Star, MapPin, Ticket } from "lucide-react";

interface AttendedCardProps {
  performance: {
    id: string;
    title: string;
    artist: string;
    venue: string;
    location: string;
    date: string;
    imageUrl: string;
    userRating: number;
  };
}

export function AttendedCard({ performance }: AttendedCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="overflow-hidden"
    >
      <Card className="h-full overflow-hidden border border-border shadow-sm transition-all duration-200 hover:shadow-md dark:border-border/30">
        <div className="relative aspect-video overflow-hidden">
          <img
            src={performance.imageUrl}
            alt={performance.title}
            className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
          <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
            <h3 className="font-medium text-sm">{performance.title}</h3>
            <p className="text-xs text-white/90">{performance.artist}</p>
          </div>
        </div>
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="h-3 w-3 text-primary/70" />
              <span>{formatDate(performance.date)}</span>
            </div>
            <div className="flex items-center gap-1 rounded-full bg-primary/5 px-2 py-0.5">
              <Star className="h-3 w-3 text-primary fill-current" />
              <span className="text-primary text-xs font-medium">
                {performance.userRating.toFixed(1)}
              </span>
            </div>
          </div>
          <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
            <MapPin className="h-3 w-3 text-muted-foreground/70" />
            <span className="truncate">
              {performance.venue}, {performance.location}
            </span>
          </div>
          <div className="mt-3 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs transition-colors"
            >
              <Ticket className="mr-1 h-3 w-3" />
              查看票据
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
