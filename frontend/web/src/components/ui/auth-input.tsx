import * as React from "react";
import { cn } from "@/lib/utils";
import { Input, InputProps } from "./input";
import { Eye, EyeOff } from "lucide-react";

export interface AuthInputProps extends InputProps {}

export const AuthInput = React.forwardRef<HTMLInputElement, AuthInputProps>(
  ({ className, ...props }, ref) => {
    return (
      <Input
        className={cn(
          "focus-visible:ring-accent/50 border-white/10 bg-white/10 text-white placeholder:text-white/50 dark:border-white/5 dark:bg-black/20 dark:text-white dark:placeholder:text-white/40 transition-all duration-200",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
AuthInput.displayName = "AuthInput";

export interface PasswordInputProps extends AuthInputProps {
  showPassword: boolean;
  onToggleShowPassword: () => void;
}

export const PasswordInput = React.forwardRef<
  HTMLInputElement,
  PasswordInputProps
>(({ className, showPassword, onToggleShowPassword, ...props }, ref) => {
  return (
    <div className="relative">
      <AuthInput
        type={showPassword ? "text" : "password"}
        className={cn("pr-10", className)}
        ref={ref}
        {...props}
      />
      <button
        type="button"
        className="absolute right-3 top-1/2 -translate-y-1/2 text-white/50 hover:text-white transition-colors"
        onClick={onToggleShowPassword}
      >
        {showPassword ? (
          <EyeOff className="h-4 w-4" />
        ) : (
          <Eye className="h-4 w-4" />
        )}
      </button>
    </div>
  );
});
PasswordInput.displayName = "PasswordInput";
