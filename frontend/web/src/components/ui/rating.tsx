import React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface RatingProps {
  value: number;
  max?: number;
  readOnly?: boolean;
  onChange?: (value: number) => void;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function Rating({
  value,
  max = 5,
  readOnly = false,
  onChange,
  className,
  size = "md",
}: RatingProps) {
  const [hoverValue, setHoverValue] = React.useState<number | null>(null);

  // 将评分四舍五入到最近的 0.5
  const roundedValue = Math.round(value * 2) / 2;

  const handleClick = (index: number) => {
    if (!readOnly && onChange) {
      onChange(index + 1);
    }
  };

  const handleMouseEnter = (index: number) => {
    if (!readOnly) {
      setHoverValue(index + 1);
    }
  };

  const handleMouseLeave = () => {
    if (!readOnly) {
      setHoverValue(null);
    }
  };

  return (
    <div
      className={cn("flex items-center", className)}
      onMouseLeave={handleMouseLeave}
    >
      {Array.from({ length: max }).map((_, index) => {
        const filled =
          hoverValue !== null ? index < hoverValue : index < roundedValue;
        const halfFilled =
          !filled && index < Math.ceil(roundedValue) && roundedValue % 1 !== 0;

        return (
          <span
            key={index}
            className={cn(
              "text-muted-foreground/30 relative cursor-default",
              !readOnly && "cursor-pointer",
              filled && "text-star",
            )}
            onClick={() => handleClick(index)}
            onMouseEnter={() => handleMouseEnter(index)}
          >
            <Star
              className={
                size === "sm"
                  ? "h-3 w-3"
                  : size === "lg"
                    ? "h-5 w-5"
                    : "h-4 w-4"
              }
            />
            {halfFilled && (
              <span className="absolute inset-0 w-[50%] overflow-hidden">
                <Star
                  className={
                    size === "sm"
                      ? "text-star h-3 w-3"
                      : size === "lg"
                        ? "text-star h-5 w-5"
                        : "text-star h-4 w-4"
                  }
                />
              </span>
            )}
          </span>
        );
      })}
    </div>
  );
}
