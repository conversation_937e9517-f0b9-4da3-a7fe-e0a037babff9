import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { motion } from "framer-motion";

export interface SocialButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  provider: "wechat" | "other";
  icon: React.ReactNode;
  isLoading?: boolean;
}

export function SocialButton({
  className,
  provider,
  icon,
  children,
  isLoading,
  ...props
}: SocialButtonProps) {
  const getProviderStyles = () => {
    switch (provider) {
      case "wechat":
        return "bg-[#07C160] hover:bg-[#07C160]/90 text-white shadow-md";
      default:
        return "bg-primary hover:bg-primary/90 text-white";
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="w-full"
    >
      <Button
        className={cn(
          "w-full transition-all duration-300",
          getProviderStyles(),
          className,
        )}
        {...props}
      >
        {isLoading ? (
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
            <span>处理中...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2">
            {icon}
            <span>{children}</span>
          </div>
        )}
      </Button>
    </motion.div>
  );
}
