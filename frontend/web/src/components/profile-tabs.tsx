import { motion } from "framer-motion";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ReviewCard } from "@/components/review-card";
import { AttendedCard } from "@/components/attended-card";
import { Review, AttendedPerformance } from "@/types";

interface ProfileTabsProps {
  reviews: Review[];
  attended: AttendedPerformance[];
  activeTab: string;
  setActiveTab: (value: string) => void;
}

export function ProfileTabs({
  reviews,
  attended,
  activeTab,
  setActiveTab,
}: ProfileTabsProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="mt-6"
    >
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="w-full bg-muted/30 p-0.5 rounded-lg">
          <TabsTrigger
            value="reviews"
            className="rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm text-muted-foreground data-[state=active]:text-foreground transition-colors"
          >
            我的评价
          </TabsTrigger>
          <TabsTrigger
            value="attended"
            className="rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm text-muted-foreground data-[state=active]:text-foreground transition-colors"
          >
            参加过的
          </TabsTrigger>
        </TabsList>

        {/* 我的评价标签页 */}
        <TabsContent value="reviews" className="mt-4">
          <div className="space-y-4">
            {reviews.length > 0 ? (
              reviews.map((review) => (
                <ReviewCard key={review.id} review={review} />
              ))
            ) : (
              <div className="rounded-lg border border-border/50 bg-muted/20 p-6 text-center text-muted-foreground">
                您还没有发表过评价
              </div>
            )}
          </div>
        </TabsContent>

        {/* 参加过的标签页 */}
        <TabsContent value="attended" className="mt-4">
          {attended.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {attended.map((performance) => (
                <AttendedCard key={performance.id} performance={performance} />
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-border/50 bg-muted/20 p-6 text-center text-muted-foreground">
              您还没有参加过演出
            </div>
          )}
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
