import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Rating } from "@/components/ui/rating";
import { formatDate, formatDateTime } from "@/lib/utils";
import { Clock, Music } from "lucide-react";

interface ReviewCardProps {
  review: {
    id: string;
    title: string;
    artist: string;
    venue: string;
    location: string;
    date: string;
    imageUrl: string;
    userRating: number;
    reviewDate: string;
    reviewText?: string;
  };
}

export function ReviewCard({ review }: ReviewCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="overflow-hidden"
    >
      <Card className="overflow-hidden border border-border shadow-sm transition-all duration-200 hover:shadow-md dark:border-border/30">
        <CardContent className="p-0">
          <div className="flex flex-col sm:flex-row">
            <div className="relative h-28 sm:h-full sm:w-28 flex-shrink-0 overflow-hidden">
              <img
                src={review.imageUrl}
                alt={review.title}
                className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity hover:opacity-100"></div>
            </div>
            <div className="flex-1 p-4">
              <div className="mb-2 flex items-start justify-between">
                <div>
                  <h3 className="font-medium text-foreground">
                    {review.title}
                  </h3>
                  <div className="flex items-center gap-1 text-muted-foreground text-sm">
                    <Music className="h-3 w-3" />
                    <span>{review.artist}</span>
                  </div>
                  <p className="text-muted-foreground/80 text-xs mt-0.5">
                    {review.venue}, {review.location} ·{" "}
                    {formatDate(review.date)}
                  </p>
                </div>
                <div className="flex items-center bg-primary/5 px-2 py-1 rounded-full">
                  <Rating value={review.userRating} readOnly size="sm" />
                  <span className="ml-1 text-sm font-medium text-primary">
                    {review.userRating.toFixed(1)}
                  </span>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {review.reviewText ||
                    "这场演出真的太棒了！音响效果一流，歌手的现场表现超出预期。特别是中间的那段即兴演奏，简直让人陶醉。"}
                </p>
                <div className="text-muted-foreground/70 mt-2 flex items-center text-xs">
                  <Clock className="mr-1 h-3 w-3" />
                  评价于 {formatDateTime(review.reviewDate)}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
