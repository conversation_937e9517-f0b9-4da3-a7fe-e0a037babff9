import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Edit, User, Mail, MapPin, Calendar } from "lucide-react";

interface ProfileCardProps {
  user: {
    id: string;
    name: string;
    username: string;
    avatar: string;
    email?: string;
    location?: string;
    joinDate?: string;
  };
  onEdit: () => void;
}

export function ProfileCard({ user, onEdit }: ProfileCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="overflow-hidden border border-border shadow-sm dark:border-border/30">
        <CardContent className="p-0">
          <div className="flex flex-col sm:flex-row sm:items-center">
            {/* 头像和基本信息 */}
            <div className="flex flex-col items-center p-6 sm:flex-row sm:gap-6 sm:border-r sm:border-border/20 sm:dark:border-border/10">
              <Avatar className="h-20 w-20 border border-border/10 shadow-sm">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="bg-primary/5 text-primary text-lg">
                  {user.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="mt-4 text-center sm:mt-0 sm:text-left">
                <h2 className="text-xl font-semibold text-foreground">
                  {user.name}
                </h2>
                <div className="flex items-center justify-center gap-1 text-muted-foreground sm:justify-start">
                  <User className="h-3.5 w-3.5" />
                  <span className="text-sm">@{user.username}</span>
                </div>
              </div>
            </div>

            {/* 联系信息和编辑按钮 */}
            <div className="flex flex-1 flex-col justify-between p-6 pt-0 sm:pt-6">
              <div className="grid gap-2 text-sm text-muted-foreground">
                {user.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-primary/60" />
                    <span>{user.email}</span>
                  </div>
                )}
                {user.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-accent/60" />
                    <span>{user.location}</span>
                  </div>
                )}
                {user.joinDate && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-primary/60" />
                    <span>加入于 {user.joinDate}</span>
                  </div>
                )}
              </div>

              <div className="mt-4 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onEdit}
                  className="transition-all hover:bg-primary/5"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  编辑资料
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
