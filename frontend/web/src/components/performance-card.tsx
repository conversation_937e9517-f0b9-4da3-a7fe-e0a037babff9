import { Calendar, MapPin, Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { Performance } from "@/types";

interface PerformanceCardProps {
  performance: Performance;
  compact?: boolean;
}

export function PerformanceCard({
  performance,
  compact = false,
}: PerformanceCardProps) {
  const {
    id,
    title,
    artist,
    venue,
    location,
    date,
    imageUrl,
    rating,
    reviewCount,
  } = performance;

  if (compact) {
    return (
      <Link to={`/performances/${id}`} className="group block">
        <div className="overflow-hidden rounded-lg bg-card shadow-sm transition-all hover:shadow-md">
          <div className="aspect-[2/3] overflow-hidden">
            <img
              src={imageUrl}
              alt={title}
              className="h-full w-full object-cover transition-transform group-hover:scale-105"
            />
          </div>
          <div className="p-3">
            <div className="mb-1 flex items-center gap-1">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">{rating.toFixed(1)}</span>
            </div>
            <h3 className="line-clamp-2 text-sm font-semibold group-hover:text-primary">
              {title}
            </h3>
            <p className="mt-1 text-xs text-muted-foreground">{artist}</p>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link to={`/performances/${id}`} className="group block">
      <Card className="overflow-hidden transition-all hover:shadow-lg">
        <div className="aspect-[4/3] overflow-hidden">
          <img
            src={imageUrl}
            alt={title}
            className="h-full w-full object-cover transition-transform group-hover:scale-105"
          />
        </div>
        <div className="p-4">
          <div className="mb-2 flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="font-medium">{rating.toFixed(1)}</span>
              <span className="text-sm text-muted-foreground">
                ({reviewCount})
              </span>
            </div>
          </div>

          <h3 className="mb-2 line-clamp-2 font-semibold group-hover:text-primary">
            {title}
          </h3>

          <p className="mb-3 text-sm text-muted-foreground">{artist}</p>

          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(date)}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span>
                {venue}, {location}
              </span>
            </div>
          </div>
        </div>
      </Card>
    </Link>
  );
}
