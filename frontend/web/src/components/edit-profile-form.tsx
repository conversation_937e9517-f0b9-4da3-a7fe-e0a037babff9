import { useState } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, X, Save } from "lucide-react";
import { User, UserProfileFormData } from "@/types";

interface EditProfileFormProps {
  user: User;
  onCancel: () => void;
  onSave: (user: UserProfileFormData) => void;
}

export function EditProfileForm({
  user,
  onCancel,
  onSave,
}: EditProfileFormProps) {
  const [formData, setFormData] = useState({
    id: user.id,
    name: user.name,
    username: user.username,
    email: user.email || "",
    location: user.location || "",
    avatar: user.avatar,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="mt-6"
    >
      <Card className="border border-border shadow-sm dark:border-border/30">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-medium text-foreground">
                编辑个人资料
              </CardTitle>
              <CardDescription>更新您的个人信息</CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onCancel}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-5">
            <div className="flex justify-center">
              <div className="relative inline-block">
                <Avatar className="h-20 w-20 border border-border/10 shadow-sm">
                  <AvatarImage src={formData.avatar} alt={formData.name} />
                  <AvatarFallback className="bg-primary/5 text-primary text-lg">
                    {formData.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <button
                  type="button"
                  className="absolute -bottom-1 -right-1 rounded-full bg-primary/10 p-1.5 text-primary ring-2 ring-background transition-colors hover:bg-primary/20"
                >
                  <Camera className="h-3.5 w-3.5" />
                </button>
              </div>
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  姓名
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="focus-visible:ring-primary/30"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium">
                  用户名
                </Label>
                <Input
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  required
                  className="focus-visible:ring-primary/30"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">
                  邮箱
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="focus-visible:ring-primary/30"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium">
                  所在地
                </Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="focus-visible:ring-primary/30"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                size="sm"
                className="transition-colors"
              >
                取消
              </Button>
              <Button
                type="submit"
                variant="default"
                size="sm"
                className="bg-primary text-primary-foreground transition-colors"
              >
                <Save className="mr-2 h-3.5 w-3.5" />
                保存
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
