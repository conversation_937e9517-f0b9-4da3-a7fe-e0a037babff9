import { Link } from "react-router-dom";
import { Github, Instagram, Twitter } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-background mt-auto w-full border-t">
      <div className="container mx-auto py-10">
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4">
          <div className="flex flex-col gap-2">
            <h3 className="text-lg font-semibold">IGigDb</h3>
            <p className="text-muted-foreground text-sm">
              互联网现场演出数据库 - 发现、评分、重温每一场难忘演出
            </p>
            <div className="mt-2 flex items-center gap-3">
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Twitter className="text-muted-foreground hover:text-accent h-5 w-5 transition-colors" />
                <span className="sr-only">Twitter</span>
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Instagram className="text-muted-foreground hover:text-accent h-5 w-5 transition-colors" />
                <span className="sr-only">Instagram</span>
              </a>
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="text-muted-foreground hover:text-accent h-5 w-5 transition-colors" />
                <span className="sr-only">GitHub</span>
              </a>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="text-base font-semibold">导航</h3>
            <ul className="flex flex-col gap-2">
              <li>
                <Link
                  to="/"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  首页
                </Link>
              </li>
              <li>
                <Link
                  to="/performances"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  演出
                </Link>
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="text-base font-semibold">公司</h3>
            <ul className="flex flex-col gap-2">
              <li>
                <Link
                  to="/about"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  关于我们
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="text-base font-semibold">法律</h3>
            <ul className="flex flex-col gap-2">
              <li>
                <Link
                  to="/terms"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  服务条款
                </Link>
              </li>
              <li>
                <Link
                  to="/privacy"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  隐私政策
                </Link>
              </li>
              <li>
                <Link
                  to="/cookies"
                  className="text-muted-foreground hover:text-accent text-sm transition-colors"
                >
                  Cookie政策
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 border-t pt-6">
          <p className="text-muted-foreground text-center text-sm">
            © {new Date().getFullYear()} IGigDb. 保留所有权利
          </p>
        </div>
      </div>
    </footer>
  );
}
