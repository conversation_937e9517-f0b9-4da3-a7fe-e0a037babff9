import React from "react";
import { motion } from "framer-motion";

interface ProfileLayoutProps {
  children: React.ReactNode;
}

export function ProfileLayout({ children }: ProfileLayoutProps) {
  return (
    <div className="min-h-[calc(100vh-80px)] w-full bg-background dark:bg-background">
      {/* 微妙的顶部渐变装饰 */}
      <div className="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-primary/80 to-accent/80"></div>

      {/* 主要内容 */}
      <div className="container py-8">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="mx-auto max-w-4xl"
        >
          {children}
        </motion.div>
      </div>
    </div>
  );
}
