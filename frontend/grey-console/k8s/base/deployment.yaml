apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${SERVICE}
  labels:
    service: ${SERVICE}
    branch: ${BRANCH}
    commit: ${COMMIT}
    datetime: ${DATETIME}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      service: ${SERVICE}
      branch: ${BRANCH}
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        service: ${SERVICE}
        branch: ${BRANCH}
        commit: ${COMMIT}
        datetime: ${DATETIME}
    spec:
      containers:
        - name: ${SERVICE}
          image: ${IMAGE} # 这将由 CI/CD 流程替换
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 1000m
              memory: 512Mi
          ports:
            - containerPort: 8080
          startupProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 2
            failureThreshold: 30
          readinessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 2
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 2
