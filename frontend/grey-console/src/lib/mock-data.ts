import { Route } from "./api";

// 模拟路由规则数据
export const mockRoutes: Route[] = [
  {
    id: 1,
    name: "灰度测试-用户分组",
    namespace: "testing",
    conditions: [
      {
        rules: [
          {
            type: "header",
            name: "X-User-Group",
            operation: "equals",
            value: "beta-testers",
          },
        ],
      },
      {
        rules: [
          {
            type: "header",
            name: "X-User-Id",
            operation: "regex",
            value: "^(1001|1002|1003)$",
          },
        ],
      },
    ],
    services: [
      {
        name: "service-app",
        weights: [
          {
            service: "service-app-dev-feature1",
            branch: "dev-feature1",
            weight: 80,
          },
          {
            service: "service-app-main",
            branch: "main",
            weight: 20,
          },
        ],
      },
    ],
    createdAt: "2023-11-15T08:30:00Z",
    updatedAt: "2023-11-16T10:15:00Z",
  },
  {
    id: 2,
    name: "地域分流",
    namespace: "production",
    conditions: [
      {
        rules: [
          {
            type: "header",
            name: "X-Region",
            operation: "equals",
            value: "cn-north",
          },
        ],
      },
    ],
    services: [
      {
        name: "service-app",
        weights: [
          {
            service: "service-app-main",
            branch: "main",
            weight: 100,
          },
        ],
      },
      {
        name: "web",
        weights: [
          {
            service: "web-dev-v2",
            branch: "dev-v2",
            weight: 50,
          },
          {
            service: "web-main",
            branch: "main",
            weight: 50,
          },
        ],
      },
    ],
    createdAt: "2023-10-20T14:25:00Z",
    updatedAt: "2023-11-05T09:40:00Z",
  },
  {
    id: 3,
    name: "移动端新版本测试",
    namespace: "testing",
    conditions: [
      {
        rules: [
          {
            type: "header",
            name: "User-Agent",
            operation: "contains",
            value: "iPhone",
          },
          {
            type: "header",
            name: "X-App-Version",
            operation: "startsWith",
            value: "2.0",
          },
        ],
      },
    ],
    services: [
      {
        name: "service-app",
        weights: [
          {
            service: "service-app-dev-mobile-v2",
            branch: "dev-mobile-v2",
            weight: 100,
          },
        ],
      },
    ],
    createdAt: "2023-11-01T11:20:00Z",
    updatedAt: "2023-11-10T16:35:00Z",
  },
  {
    id: 4,
    name: "内部测试环境",
    namespace: "testing",
    conditions: [
      {
        rules: [
          {
            type: "host",
            name: "",
            operation: "equals",
            value: "internal.example.com",
          },
        ],
      },
    ],
    services: [
      {
        name: "service-app",
        weights: [
          {
            service: "service-app-dev-internal",
            branch: "dev-internal",
            weight: 100,
          },
        ],
      },
      {
        name: "service-crawler",
        weights: [
          {
            service: "service-crawler-dev-v2",
            branch: "dev-v2",
            weight: 100,
          },
        ],
      },
    ],
    createdAt: "2023-09-15T09:10:00Z",
    updatedAt: "2023-11-12T13:45:00Z",
  },
  {
    id: 5,
    name: "路径匹配测试",
    namespace: "testing",
    conditions: [
      {
        rules: [
          {
            type: "path",
            name: "",
            operation: "startsWith",
            value: "/api/v2/",
          },
        ],
      },
    ],
    services: [
      {
        name: "service-app",
        weights: [
          {
            service: "service-app-dev-api-v2",
            branch: "dev-api-v2",
            weight: 90,
          },
          {
            service: "service-app-main",
            branch: "main",
            weight: 10,
          },
        ],
      },
    ],
    createdAt: "2023-10-05T15:30:00Z",
    updatedAt: "2023-11-08T11:20:00Z",
  },
];

// 模拟服务数据
export const mockServices: Record<string, Record<string, string[]>> = {
  testing: {
    "service-app": ["main", "dev-feature1", "dev-mobile-v2", "dev-internal", "dev-api-v2"],
    "service-crawler": ["main", "dev-v2"],
    "service-task": ["main", "dev-batch"],
    web: ["main", "dev-v2"],
  },
  production: {
    "service-app": ["main", "stable-v1"],
    "service-crawler": ["main"],
    "service-task": ["main"],
    web: ["main", "stable-v1"],
  },
};

// 模拟API函数
export const mockGetRoutes = (): Promise<Route[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockRoutes);
    }, 500);
  });
};

export const mockGetRouteById = (id: number): Promise<Route> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const route = mockRoutes.find((r) => r.id === id);
      if (route) {
        resolve(route);
      } else {
        reject(new Error("Route not found"));
      }
    }, 500);
  });
};

export const mockGetServices = (): Promise<Record<string, Record<string, string[]>>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockServices);
    }, 500);
  });
};

export const mockGetServicesByNamespace = (
  namespace: string
): Promise<Record<string, string[]>> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const services = mockServices[namespace];
      if (services) {
        resolve(services);
      } else {
        reject(new Error("Namespace not found"));
      }
    }, 500);
  });
};
