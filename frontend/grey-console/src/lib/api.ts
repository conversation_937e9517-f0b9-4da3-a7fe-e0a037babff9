import axios from "axios";
import {
  mockGetRoutes,
  mockGetRouteById,
  mockGetServices,
  mockGetServicesByNamespace,
} from "./mock-data";

// 是否使用模拟数据
const USE_MOCK = true;

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:8080/api",
  headers: {
    "Content-Type": "application/json",
  },
});

// 路由规则类型
export interface Route {
  id: number;
  name: string;
  namespace: string;
  conditions: Condition[];
  services: ServiceConfig[];
  createdAt: string;
  updatedAt: string;
}

// 条件组类型
export interface Condition {
  rules: Rule[];
}

// 规则类型
export interface Rule {
  type: string; // header, host, path
  name: string; // 对于 header 类型，表示 header 名称
  operation: string; // equals, contains, startsWith, endsWith, regex
  value: string; // 值
}

// 服务权重类型
export interface ServiceWeight {
  service: string; // 服务名称
  branch: string; // 分支名称
  weight: number; // 权重百分比
}

// 服务配置类型
export interface ServiceConfig {
  name: string; // 服务基础名称
  weights: ServiceWeight[]; // 权重配置
}

// Istio VirtualService 类型
export interface VirtualService {
  metadata: {
    name: string;
    namespace: string;
  };
  spec: {
    hosts: string[];
    http: Array<{
      route: Array<{
        destination: {
          host: string;
          subset: string;
        };
      }>;
    }>;
  };
}

// Istio DestinationRule 类型
export interface DestinationRule {
  metadata: {
    name: string;
    namespace: string;
  };
  spec: {
    host: string;
    subsets: Array<{
      name: string;
      labels: Record<string, string>;
    }>;
  };
}

// API 函数

// 获取所有路由规则
export const getRoutes = async (): Promise<Route[]> => {
  if (USE_MOCK) {
    return mockGetRoutes();
  }
  const response = await api.get("/routes");
  return response.data;
};

// 根据 ID 获取路由规则
export const getRouteById = async (id: number): Promise<Route> => {
  if (USE_MOCK) {
    return mockGetRouteById(id);
  }
  const response = await api.get(`/routes/${id}`);
  return response.data;
};

// 创建路由规则
export const createRoute = async (
  route: Omit<Route, "id" | "createdAt" | "updatedAt">
): Promise<Route> => {
  if (USE_MOCK) {
    // 模拟创建路由规则
    return new Promise((resolve) => {
      setTimeout(() => {
        const newRoute: Route = {
          ...route,
          id: Math.floor(Math.random() * 1000) + 10,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        resolve(newRoute);
      }, 500);
    });
  }
  const response = await api.post("/routes", route);
  return response.data;
};

// 更新路由规则
export const updateRoute = async (
  id: number,
  route: Omit<Route, "id" | "createdAt" | "updatedAt">
): Promise<Route> => {
  if (USE_MOCK) {
    // 模拟更新路由规则
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const updatedRoute: Route = {
            ...route,
            id,
            createdAt: "2023-11-01T00:00:00Z", // 假设的创建时间
            updatedAt: new Date().toISOString(),
          };
          resolve(updatedRoute);
        } catch (error) {
          reject(new Error("Failed to update route"));
        }
      }, 500);
    });
  }
  const response = await api.put(`/routes/${id}`, route);
  return response.data;
};

// 删除路由规则
export const deleteRoute = async (id: number): Promise<void> => {
  if (USE_MOCK) {
    // 模拟删除路由规则
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  }
  await api.delete(`/routes/${id}`);
};

// 获取所有命名空间的服务
export const getServices = async (): Promise<Record<string, Record<string, string[]>>> => {
  if (USE_MOCK) {
    return mockGetServices();
  }
  const response = await api.get("/services");
  return response.data;
};

// 获取指定命名空间的服务
export const getServicesByNamespace = async (
  namespace: string
): Promise<Record<string, string[]>> => {
  if (USE_MOCK) {
    return mockGetServicesByNamespace(namespace);
  }
  const response = await api.get(`/services/${namespace}`);
  return response.data;
};

// 获取指定命名空间的 VirtualService
export const getVirtualServices = async (namespace: string): Promise<VirtualService[]> => {
  if (USE_MOCK) {
    // 模拟 VirtualService 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            metadata: { name: "service-app", namespace },
            spec: {
              hosts: ["service-app"],
              http: [{ route: [{ destination: { host: "service-app", subset: "main" } }] }],
            },
          },
        ]);
      }, 500);
    });
  }
  const response = await api.get(`/istio/virtualservices/${namespace}`);
  return response.data;
};

// 获取指定命名空间的 DestinationRule
export const getDestinationRules = async (namespace: string): Promise<DestinationRule[]> => {
  if (USE_MOCK) {
    // 模拟 DestinationRule 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            metadata: { name: "service-app", namespace },
            spec: { host: "service-app", subsets: [{ name: "main", labels: { version: "v1" } }] },
          },
        ]);
      }, 500);
    });
  }
  const response = await api.get(`/istio/destinationrules/${namespace}`);
  return response.data;
};
