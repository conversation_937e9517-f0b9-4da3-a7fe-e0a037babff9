import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  getServicesByNamespace,
  Route,
  Condition,
  Rule,
  ServiceConfig,
  ServiceWeight,
} from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Trash2, Save } from "lucide-react";

interface RouteFormProps {
  initialData?: Route;
  onSubmit: (data: Omit<Route, "id" | "createdAt" | "updatedAt">) => void;
  isSubmitting?: boolean;
}

export function RouteForm({ initialData, onSubmit, isSubmitting = false }: RouteFormProps) {
  // 表单状态
  const [name, setName] = useState(initialData?.name || "");
  const [namespace, setNamespace] = useState(initialData?.namespace || "testing");
  const [conditions, setConditions] = useState<Condition[]>(
    initialData?.conditions || [
      { rules: [{ type: "header", name: "", operation: "equals", value: "" }] },
    ]
  );
  const [services, setServices] = useState<ServiceConfig[]>(
    initialData?.services || [{ name: "", weights: [{ service: "", branch: "main", weight: 100 }] }]
  );
  const [activeTab, setActiveTab] = useState("basic");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 获取命名空间中的服务
  const { data: namespaceServices = {}, isLoading: isLoadingServices } = useQuery({
    queryKey: ["services", namespace],
    queryFn: () => getServicesByNamespace(namespace),
    enabled: !!namespace,
  });

  // 添加条件组
  const addConditionGroup = () => {
    setConditions([
      ...conditions,
      { rules: [{ type: "header", name: "", operation: "equals", value: "" }] },
    ]);
  };

  // 删除条件组
  const removeConditionGroup = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index));
  };

  // 添加规则
  const addRule = (conditionIndex: number) => {
    const newConditions = [...conditions];
    newConditions[conditionIndex].rules.push({
      type: "header",
      name: "",
      operation: "equals",
      value: "",
    });
    setConditions(newConditions);
  };

  // 删除规则
  const removeRule = (conditionIndex: number, ruleIndex: number) => {
    const newConditions = [...conditions];
    newConditions[conditionIndex].rules = newConditions[conditionIndex].rules.filter(
      (_, i) => i !== ruleIndex
    );
    setConditions(newConditions);
  };

  // 更新规则
  const updateRule = (
    conditionIndex: number,
    ruleIndex: number,
    field: keyof Rule,
    value: string
  ) => {
    const newConditions = [...conditions];
    newConditions[conditionIndex].rules[ruleIndex][field] = value;
    setConditions(newConditions);
  };

  // 添加服务
  const addService = () => {
    setServices([
      ...services,
      { name: "", weights: [{ service: "", branch: "main", weight: 100 }] },
    ]);
  };

  // 删除服务
  const removeService = (index: number) => {
    setServices(services.filter((_, i) => i !== index));
  };

  // 添加权重
  const addWeight = (serviceIndex: number) => {
    const newServices = [...services];
    newServices[serviceIndex].weights.push({
      service: "",
      branch: "",
      weight: 0,
    });
    setServices(newServices);
  };

  // 删除权重
  const removeWeight = (serviceIndex: number, weightIndex: number) => {
    const newServices = [...services];
    newServices[serviceIndex].weights = newServices[serviceIndex].weights.filter(
      (_, i) => i !== weightIndex
    );
    setServices(newServices);
  };

  // 更新服务
  const updateService = (serviceIndex: number, field: keyof ServiceConfig, value: string) => {
    const newServices = [...services];
    if (field === "name") {
      newServices[serviceIndex].name = value;
    }
    setServices(newServices);
  };

  // 更新权重
  const updateWeight = (
    serviceIndex: number,
    weightIndex: number,
    field: keyof ServiceWeight,
    value: string | number
  ) => {
    const newServices = [...services];
    if (field === "weight") {
      newServices[serviceIndex].weights[weightIndex].weight = Number(value);
    } else if (field === "branch") {
      newServices[serviceIndex].weights[weightIndex].branch = value as string;
    } else if (field === "service") {
      newServices[serviceIndex].weights[weightIndex].service = value as string;
    }
    setServices(newServices);
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "名称不能为空";
    }

    if (!namespace.trim()) {
      newErrors.namespace = "命名空间不能为空";
    }

    // 验证条件组
    conditions.forEach((condition, conditionIndex) => {
      condition.rules.forEach((rule, ruleIndex) => {
        if (rule.type === "header" && !rule.name.trim()) {
          newErrors[`condition_${conditionIndex}_rule_${ruleIndex}_name`] = "请求头名称不能为空";
        }
        if (!rule.value.trim()) {
          newErrors[`condition_${conditionIndex}_rule_${ruleIndex}_value`] = "值不能为空";
        }
      });
    });

    // 验证服务
    services.forEach((service, serviceIndex) => {
      if (!service.name.trim()) {
        newErrors[`service_${serviceIndex}_name`] = "服务名称不能为空";
      }

      // 验证权重总和为 100
      const totalWeight = service.weights.reduce((sum, weight) => sum + weight.weight, 0);
      if (totalWeight !== 100) {
        newErrors[`service_${serviceIndex}_weights`] = `权重总和必须为 100，当前为 ${totalWeight}`;
      }

      service.weights.forEach((weight, weightIndex) => {
        if (!weight.branch.trim()) {
          newErrors[`service_${serviceIndex}_weight_${weightIndex}_branch`] = "分支不能为空";
        }
      });
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // 准备提交数据
    const formData: Omit<Route, "id" | "createdAt" | "updatedAt"> = {
      name,
      namespace,
      conditions,
      services,
    };

    onSubmit(formData);
  };

  // 当选择服务时，自动填充分支
  const handleServiceSelect = (serviceIndex: number, serviceName: string) => {
    updateService(serviceIndex, "name", serviceName);

    // 获取服务的分支
    const branches = namespaceServices[serviceName] || [];

    // 更新权重
    const newServices = [...services];
    newServices[serviceIndex].weights = branches.map((branch, index) => ({
      service: serviceName + (branch === "main" ? "-main" : `-${branch}`),
      branch,
      weight: index === 0 ? 100 : 0, // 第一个分支权重为 100，其他为 0
    }));

    setServices(newServices);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="conditions">条件</TabsTrigger>
          <TabsTrigger value="services">服务</TabsTrigger>
        </TabsList>
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>设置路由规则的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">名称</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="输入路由规则名称"
                  className={errors.name ? "border-destructive" : ""}
                />
                {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="namespace">命名空间</Label>
                <Select value={namespace} onValueChange={(value) => setNamespace(value)}>
                  <SelectTrigger
                    id="namespace"
                    className={errors.namespace ? "border-destructive" : ""}
                  >
                    <SelectValue placeholder="选择命名空间" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="testing">testing</SelectItem>
                    <SelectItem value="production">production</SelectItem>
                  </SelectContent>
                </Select>
                {errors.namespace && <p className="text-sm text-destructive">{errors.namespace}</p>}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="conditions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>条件组</CardTitle>
              <CardDescription>
                设置路由规则的条件组，多个条件组之间是"或"的关系，条件组内的规则是"与"的关系
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {conditions.map((condition, conditionIndex) => (
                <div key={conditionIndex} className="rounded-lg border p-4">
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-semibold">条件组 {conditionIndex + 1}</h3>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeConditionGroup(conditionIndex)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {condition.rules.map((rule, ruleIndex) => (
                      <div key={ruleIndex} className="rounded-md bg-muted p-4">
                        <div className="mb-2 flex items-center justify-between">
                          <h4 className="font-medium">规则 {ruleIndex + 1}</h4>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeRule(conditionIndex, ruleIndex)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                          <div className="space-y-2">
                            <Label>类型</Label>
                            <Select
                              value={rule.type}
                              onValueChange={(value) =>
                                updateRule(conditionIndex, ruleIndex, "type", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择类型" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="header">请求头</SelectItem>
                                <SelectItem value="host">主机</SelectItem>
                                <SelectItem value="path">路径</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          {rule.type === "header" && (
                            <div className="space-y-2">
                              <Label>请求头名称</Label>
                              <Input
                                value={rule.name}
                                onChange={(e) =>
                                  updateRule(conditionIndex, ruleIndex, "name", e.target.value)
                                }
                                placeholder="输入请求头名称"
                                className={
                                  errors[`condition_${conditionIndex}_rule_${ruleIndex}_name`]
                                    ? "border-destructive"
                                    : ""
                                }
                              />
                              {errors[`condition_${conditionIndex}_rule_${ruleIndex}_name`] && (
                                <p className="text-sm text-destructive">
                                  {errors[`condition_${conditionIndex}_rule_${ruleIndex}_name`]}
                                </p>
                              )}
                            </div>
                          )}
                          <div className="space-y-2">
                            <Label>操作</Label>
                            <Select
                              value={rule.operation}
                              onValueChange={(value) =>
                                updateRule(conditionIndex, ruleIndex, "operation", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="选择操作" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="equals">等于</SelectItem>
                                <SelectItem value="contains">包含</SelectItem>
                                <SelectItem value="startsWith">开头是</SelectItem>
                                <SelectItem value="regex">正则匹配</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>值</Label>
                            <Input
                              value={rule.value}
                              onChange={(e) =>
                                updateRule(conditionIndex, ruleIndex, "value", e.target.value)
                              }
                              placeholder="输入值"
                              className={
                                errors[`condition_${conditionIndex}_rule_${ruleIndex}_value`]
                                  ? "border-destructive"
                                  : ""
                              }
                            />
                            {errors[`condition_${conditionIndex}_rule_${ruleIndex}_value`] && (
                              <p className="text-sm text-destructive">
                                {errors[`condition_${conditionIndex}_rule_${ruleIndex}_value`]}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => addRule(conditionIndex)}
                      className="w-full"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      添加规则
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={addConditionGroup}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                添加条件组
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>服务配置</CardTitle>
              <CardDescription>设置路由规则的服务和权重配置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {services.map((service, serviceIndex) => (
                <div key={serviceIndex} className="rounded-lg border p-4">
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-semibold">服务 {serviceIndex + 1}</h3>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeService(serviceIndex)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>服务名称</Label>
                      <Select
                        value={service.name}
                        onValueChange={(value) => handleServiceSelect(serviceIndex, value)}
                      >
                        <SelectTrigger
                          className={
                            errors[`service_${serviceIndex}_name`] ? "border-destructive" : ""
                          }
                        >
                          <SelectValue placeholder="选择服务" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingServices ? (
                            <SelectItem value="" disabled>
                              加载中...
                            </SelectItem>
                          ) : (
                            Object.keys(namespaceServices).map((serviceName) => (
                              <SelectItem key={serviceName} value={serviceName}>
                                {serviceName}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      {errors[`service_${serviceIndex}_name`] && (
                        <p className="text-sm text-destructive">
                          {errors[`service_${serviceIndex}_name`]}
                        </p>
                      )}
                    </div>
                    {errors[`service_${serviceIndex}_weights`] && (
                      <p className="text-sm text-destructive">
                        {errors[`service_${serviceIndex}_weights`]}
                      </p>
                    )}
                    <div className="space-y-2">
                      <Label>权重配置</Label>
                      <div className="space-y-2">
                        {service.weights.map((weight, weightIndex) => (
                          <div
                            key={weightIndex}
                            className="grid items-center gap-4 rounded-md bg-muted p-3 sm:grid-cols-3"
                          >
                            <div className="space-y-1">
                              <Label className="text-xs">分支</Label>
                              <Select
                                value={weight.branch}
                                onValueChange={(value) =>
                                  updateWeight(serviceIndex, weightIndex, "branch", value)
                                }
                              >
                                <SelectTrigger
                                  className={
                                    errors[`service_${serviceIndex}_weight_${weightIndex}_branch`]
                                      ? "border-destructive"
                                      : ""
                                  }
                                >
                                  <SelectValue placeholder="选择分支" />
                                </SelectTrigger>
                                <SelectContent>
                                  {service.name && namespaceServices[service.name]
                                    ? namespaceServices[service.name].map((branch) => (
                                        <SelectItem key={branch} value={branch}>
                                          {branch}
                                        </SelectItem>
                                      ))
                                    : [
                                        <SelectItem key="main" value="main">
                                          main
                                        </SelectItem>,
                                      ]}
                                </SelectContent>
                              </Select>
                              {errors[`service_${serviceIndex}_weight_${weightIndex}_branch`] && (
                                <p className="text-xs text-destructive">
                                  {errors[`service_${serviceIndex}_weight_${weightIndex}_branch`]}
                                </p>
                              )}
                            </div>
                            <div className="space-y-1">
                              <Label className="text-xs">服务</Label>
                              <Input
                                value={weight.service}
                                onChange={(e) =>
                                  updateWeight(serviceIndex, weightIndex, "service", e.target.value)
                                }
                                placeholder="服务名称"
                                disabled
                              />
                            </div>
                            <div className="space-y-1">
                              <div className="flex items-center justify-between">
                                <Label className="text-xs">权重 (%)</Label>
                                {weightIndex > 0 && (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6"
                                    onClick={() => removeWeight(serviceIndex, weightIndex)}
                                  >
                                    <Trash2 className="h-3 w-3 text-destructive" />
                                  </Button>
                                )}
                              </div>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                value={weight.weight}
                                onChange={(e) =>
                                  updateWeight(
                                    serviceIndex,
                                    weightIndex,
                                    "weight",
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                placeholder="权重"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => addWeight(serviceIndex)}
                        className="mt-2 w-full"
                        disabled={
                          !service.name ||
                          !namespaceServices[service.name] ||
                          service.weights.length >= namespaceServices[service.name].length
                        }
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        添加权重
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addService} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                添加服务
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-6 flex justify-end gap-2">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "保存中..." : "保存"}
          {!isSubmitting && <Save className="ml-2 h-4 w-4" />}
        </Button>
      </div>
    </form>
  );
}
