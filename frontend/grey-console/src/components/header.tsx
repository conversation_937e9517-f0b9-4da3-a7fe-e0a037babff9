import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { ModeToggle } from "./mode-toggle";
import { Button } from "./ui/button";
import { Menu, X } from "lucide-react";

export function Header() {
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 切换移动菜单
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <header
        className={`sticky top-0 z-50 w-full transition-all duration-300 ${
          isScrolled ? "border-b bg-background/95 backdrop-blur-md" : "bg-transparent"
        }`}
      >
        <div className="container mx-auto flex h-16 items-center justify-between">
          <div className="flex items-center gap-6 md:gap-10">
            <Link to="/" className="flex items-center space-x-2">
              <span className="font-heading text-2xl font-bold">
                I<span className="text-gradient">Gig</span>Db
              </span>
            </Link>
            <nav className="hidden gap-6 md:flex">
              <Link
                to="/"
                className={`text-sm font-medium transition-colors hover:text-accent ${
                  location.pathname === "/" ? "text-accent" : "text-foreground/60"
                }`}
              >
                控制台
              </Link>
              <Link
                to="/routes"
                className={`text-sm font-medium transition-colors hover:text-accent ${
                  location.pathname.startsWith("/routes") ? "text-accent" : "text-foreground/60"
                }`}
              >
                路由规则
              </Link>
              <Link
                to="/services"
                className={`text-sm font-medium transition-colors hover:text-accent ${
                  location.pathname === "/services" ? "text-accent" : "text-foreground/60"
                }`}
              >
                服务
              </Link>
              <Link
                to="/test"
                className={`text-sm font-medium transition-colors hover:text-accent ${
                  location.pathname === "/test" ? "text-accent" : "text-foreground/60"
                }`}
              >
                测试
              </Link>
            </nav>
          </div>

          <div className="flex items-center gap-2">
            <ModeToggle />

            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleMobileMenu}
              aria-label="菜单"
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* 移动菜单 */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="container mx-auto py-4">
              <nav className="flex flex-col space-y-4">
                <Link
                  to="/"
                  className={`text-sm font-medium transition-colors hover:text-accent ${
                    location.pathname === "/" ? "text-accent" : "text-foreground/60"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  控制台
                </Link>
                <Link
                  to="/routes"
                  className={`text-sm font-medium transition-colors hover:text-accent ${
                    location.pathname.startsWith("/routes") ? "text-accent" : "text-foreground/60"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  路由规则
                </Link>
                <Link
                  to="/services"
                  className={`text-sm font-medium transition-colors hover:text-accent ${
                    location.pathname === "/services" ? "text-accent" : "text-foreground/60"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  服务
                </Link>
                <Link
                  to="/test"
                  className={`text-sm font-medium transition-colors hover:text-accent ${
                    location.pathname === "/test" ? "text-accent" : "text-foreground/60"
                  }`}
                  onClick={toggleMobileMenu}
                >
                  测试
                </Link>
              </nav>
            </div>
          </div>
        )}
      </header>
    </>
  );
}
