import { Link } from "react-router-dom";

export function Footer() {
  return (
    <footer className="border-t py-6 md:py-0">
      <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
        <p className="text-center text-sm text-muted-foreground md:text-left">
          &copy; {new Date().getFullYear()} IGigDb. 保留所有权利.
        </p>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <Link to="/" className="hover:text-foreground">
            控制台
          </Link>
          <Link to="/routes" className="hover:text-foreground">
            路由规则
          </Link>
          <Link to="/services" className="hover:text-foreground">
            服务
          </Link>
          <Link to="/test" className="hover:text-foreground">
            测试页面
          </Link>
        </div>
      </div>
    </footer>
  );
}
