import { useState } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getRouteById, updateRoute, deleteRoute, Route } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ArrowLeft, Edit, Trash2 } from "lucide-react";
import { formatDateTime } from "@/lib/utils";
import { RouteForm } from "@/components/route-form";
import { Loading } from "@/components/ui/loading";

export default function RouteDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // 获取路由规则详情
  const {
    data: route,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["route", id],
    queryFn: () => getRouteById(Number(id)),
    enabled: !!id,
  });

  // 更新路由规则
  const updateMutation = useMutation({
    mutationFn: (updatedRoute: Omit<Route, "id" | "createdAt" | "updatedAt">) =>
      updateRoute(Number(id), updatedRoute),
    onSuccess: () => {
      toast({
        title: "更新成功",
        description: "路由规则已成功更新",
      });
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: ["route", id] });
    },
    onError: () => {
      toast({
        title: "更新失败",
        description: "无法更新路由规则，请稍后重试",
        variant: "destructive",
      });
    },
  });

  // 删除路由规则
  const deleteMutation = useMutation({
    mutationFn: () => deleteRoute(Number(id)),
    onSuccess: () => {
      toast({
        title: "删除成功",
        description: "路由规则已成功删除",
      });
      navigate("/routes");
      queryClient.invalidateQueries({ queryKey: ["routes"] });
    },
    onError: () => {
      toast({
        title: "删除失败",
        description: "无法删除路由规则，请稍后重试",
        variant: "destructive",
      });
    },
  });

  // 处理表单提交
  const handleSubmit = (formData: Omit<Route, "id" | "createdAt" | "updatedAt">) => {
    updateMutation.mutate(formData);
  };

  // 处理删除
  const handleDelete = () => {
    deleteMutation.mutate();
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="container">
        <div className="flex h-[400px] items-center justify-center">
          <Loading text="加载中..." />
        </div>
      </div>
    );
  }

  // 如果发生错误，显示错误信息
  if (error || !route) {
    return (
      <div className="container">
        <div className="flex h-[400px] flex-col items-center justify-center">
          <p className="mb-4 text-destructive">无法加载路由规则</p>
          <Button asChild>
            <Link to="/routes">返回列表</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link to="/routes">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{route.name}</h1>
            <p className="text-muted-foreground">命名空间: {route.namespace}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {!isEditing && (
            <>
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认删除</AlertDialogTitle>
                    <AlertDialogDescription>
                      确定要删除路由规则 "{route.name}" 吗？此操作无法撤销。
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete}>删除</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
          {isEditing && (
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              取消编辑
            </Button>
          )}
        </div>
      </div>

      {isEditing ? (
        <RouteForm
          initialData={route}
          onSubmit={handleSubmit}
          isSubmitting={updateMutation.isLoading}
        />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="conditions">条件</TabsTrigger>
            <TabsTrigger value="services">服务</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>路由规则的基本信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">名称</p>
                    <p className="text-lg">{route.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">命名空间</p>
                    <p className="text-lg">{route.namespace}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">创建时间</p>
                    <p className="text-lg">{formatDateTime(route.createdAt)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">更新时间</p>
                    <p className="text-lg">{formatDateTime(route.updatedAt)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="conditions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>条件组</CardTitle>
                <CardDescription>路由规则的条件组配置</CardDescription>
              </CardHeader>
              <CardContent>
                {Array.isArray(route.conditions) && route.conditions.length > 0 ? (
                  <div className="space-y-6">
                    {route.conditions.map((condition, index) => (
                      <div key={index} className="rounded-lg border p-4">
                        <h3 className="mb-2 text-lg font-semibold">条件组 {index + 1}</h3>
                        <div className="space-y-4">
                          {condition.rules.map((rule, ruleIndex) => (
                            <div key={ruleIndex} className="rounded-md bg-muted p-3">
                              <div className="grid gap-2 sm:grid-cols-3">
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">类型</p>
                                  <p>
                                    {rule.type === "header"
                                      ? "请求头"
                                      : rule.type === "host"
                                        ? "主机"
                                        : "路径"}
                                  </p>
                                </div>
                                {rule.type === "header" && (
                                  <div>
                                    <p className="text-sm font-medium text-muted-foreground">
                                      名称
                                    </p>
                                    <p>{rule.name}</p>
                                  </div>
                                )}
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">操作</p>
                                  <p>
                                    {rule.operation === "equals"
                                      ? "等于"
                                      : rule.operation === "contains"
                                        ? "包含"
                                        : rule.operation === "startsWith"
                                          ? "开头是"
                                          : rule.operation === "regex"
                                            ? "正则匹配"
                                            : rule.operation}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">值</p>
                                  <p>{rule.value}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground">暂无条件组</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="services" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>服务配置</CardTitle>
                <CardDescription>路由规则的服务和权重配置</CardDescription>
              </CardHeader>
              <CardContent>
                {Array.isArray(route.services) && route.services.length > 0 ? (
                  <div className="space-y-6">
                    {route.services.map((service, index) => (
                      <div key={index} className="rounded-lg border p-4">
                        <h3 className="mb-2 text-lg font-semibold">{service.name}</h3>
                        <div className="space-y-4">
                          {service.weights.map((weight, weightIndex) => (
                            <div
                              key={weightIndex}
                              className="flex items-center justify-between rounded-md bg-muted p-3"
                            >
                              <div>
                                <p className="font-medium">{weight.branch}</p>
                                <p className="text-sm text-muted-foreground">分支</p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">{weight.weight}%</p>
                                <p className="text-sm text-muted-foreground">权重</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground">暂无服务配置</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
