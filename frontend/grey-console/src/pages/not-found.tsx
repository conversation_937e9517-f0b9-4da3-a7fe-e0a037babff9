import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function NotFoundPage() {
  return (
    <div className="container flex h-[70vh] flex-col items-center justify-center">
      <div className="text-center">
        <h1 className="mb-4 text-9xl font-bold text-muted-foreground">404</h1>
        <h2 className="mb-8 text-2xl font-semibold">页面未找到</h2>
        <p className="mb-8 text-muted-foreground">您访问的页面不存在或已被移除。</p>
        <Button asChild>
          <Link to="/">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回首页
          </Link>
        </Button>
      </div>
    </div>
  );
}
