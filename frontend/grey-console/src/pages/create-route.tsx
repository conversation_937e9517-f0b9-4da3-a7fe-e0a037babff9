import { useNavigate, <PERSON> } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createRoute, Route } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { RouteForm } from "@/components/route-form";

export default function CreateRoutePage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 创建路由规则
  const createMutation = useMutation({
    mutationFn: (route: Omit<Route, "id" | "createdAt" | "updatedAt">) => createRoute(route),
    onSuccess: (data) => {
      toast({
        title: "创建成功",
        description: "路由规则已成功创建",
      });
      queryClient.invalidateQueries({ queryKey: ["routes"] });
      navigate(`/routes/${data.id}`);
    },
    onError: () => {
      toast({
        title: "创建失败",
        description: "无法创建路由规则，请稍后重试",
        variant: "destructive",
      });
    },
  });

  // 处理表单提交
  const handleSubmit = (formData: Omit<Route, "id" | "createdAt" | "updatedAt">) => {
    createMutation.mutate(formData);
  };

  return (
    <div className="container">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link to="/routes">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">创建路由规则</h1>
            <p className="text-muted-foreground">创建新的灰度发布路由规则</p>
          </div>
        </div>
      </div>

      <RouteForm onSubmit={handleSubmit} isSubmitting={createMutation.isLoading} />
    </div>
  );
}
