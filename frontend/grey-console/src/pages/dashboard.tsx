import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { getRoutes, getServices } from "@/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle, Route, Server } from "lucide-react";
import { Loading } from "@/components/ui/loading";

export default function Dashboard() {
  // 获取路由规则
  const { data: routes = [], isLoading: isLoadingRoutes } = useQuery({
    queryKey: ["routes"],
    queryFn: getRoutes,
  });

  // 获取服务
  const { data: services = {}, isLoading: isLoadingServices } = useQuery({
    queryKey: ["services"],
    queryFn: getServices,
  });

  // 计算服务总数
  const serviceCount = Object.values(services).reduce((acc, namespaceServices) => {
    return acc + Object.keys(namespaceServices).length;
  }, 0);

  return (
    <div className="container">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">灰度控制台</h1>
          <p className="text-muted-foreground">配置、查看和调试路由规则</p>
        </div>
        <Button asChild>
          <Link to="/routes/create">
            <PlusCircle className="mr-2 h-4 w-4" />
            创建路由规则
          </Link>
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">路由规则</CardTitle>
            <Route className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoadingRoutes ? <Loading size="sm" /> : routes.length}
            </div>
            <p className="text-xs text-muted-foreground">已配置的路由规则总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">服务</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoadingServices ? <Loading size="sm" /> : serviceCount}
            </div>
            <p className="text-xs text-muted-foreground">所有命名空间中的服务总数</p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Tabs defaultValue="routes" className="w-full">
          <TabsList>
            <TabsTrigger value="routes">最近的路由规则</TabsTrigger>
            <TabsTrigger value="services">服务概览</TabsTrigger>
          </TabsList>
          <TabsContent value="routes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>路由规则</CardTitle>
                <CardDescription>最近创建或更新的路由规则</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingRoutes ? (
                  <div className="flex h-40 items-center justify-center">
                    <Loading text="加载中..." />
                  </div>
                ) : routes.length > 0 ? (
                  <div className="space-y-2">
                    {routes.slice(0, 5).map((route) => (
                      <div
                        key={route.id}
                        className="flex items-center justify-between rounded-lg border p-3"
                      >
                        <div>
                          <p className="font-medium">{route.name}</p>
                          <p className="text-sm text-muted-foreground">
                            命名空间: {route.namespace}
                          </p>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/routes/${route.id}`}>查看</Link>
                        </Button>
                      </div>
                    ))}
                    {routes.length > 5 && (
                      <Button variant="outline" className="w-full" asChild>
                        <Link to="/routes">查看全部</Link>
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8">
                    <p className="mb-4 text-muted-foreground">暂无路由规则</p>
                    <Button asChild>
                      <Link to="/routes/create">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        创建路由规则
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="services" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>服务概览</CardTitle>
                <CardDescription>各命名空间中的服务</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingServices ? (
                  <div className="flex h-40 items-center justify-center">
                    <Loading text="加载中..." />
                  </div>
                ) : Object.keys(services).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(services).map(([namespace, namespaceServices]) => (
                      <div key={namespace}>
                        <h3 className="mb-2 font-semibold">{namespace}</h3>
                        <div className="space-y-2">
                          {Object.entries(namespaceServices)
                            .slice(0, 3)
                            .map(([serviceName, branches]) => (
                              <div key={serviceName} className="rounded-lg border p-3">
                                <p className="font-medium">{serviceName}</p>
                                <p className="text-sm text-muted-foreground">
                                  分支: {branches.join(", ")}
                                </p>
                              </div>
                            ))}
                          {Object.keys(namespaceServices).length > 3 && (
                            <p className="text-sm text-muted-foreground">
                              ...以及 {Object.keys(namespaceServices).length - 3} 个其他服务
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                    <Button variant="outline" className="w-full" asChild>
                      <Link to="/services">查看全部</Link>
                    </Button>
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground">暂无服务数据</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
