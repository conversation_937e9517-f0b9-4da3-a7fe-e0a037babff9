import { useState } from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getRoutes, deleteRoute, Route } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { PlusCircle, Search, Trash2, Eye } from "lucide-react";
import { formatDateTime } from "@/lib/utils";
import { Loading } from "@/components/ui/loading";

export default function RoutesPage() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [routeToDelete, setRouteToDelete] = useState<Route | null>(null);

  // 获取路由规则
  const {
    data: routes = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["routes"],
    queryFn: getRoutes,
  });

  // 过滤路由规则
  const filteredRoutes = routes.filter(
    (route) =>
      route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      route.namespace.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 处理删除
  const handleDelete = async () => {
    if (!routeToDelete) return;

    try {
      await deleteRoute(routeToDelete.id);
      toast({
        title: "删除成功",
        description: `路由规则 ${routeToDelete.name} 已删除`,
      });
      refetch();
    } catch (error) {
      toast({
        title: "删除失败",
        description: "无法删除路由规则，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setRouteToDelete(null);
    }
  };

  return (
    <div className="container">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">路由规则</h1>
          <p className="text-muted-foreground">管理灰度发布的路由规则</p>
        </div>
        <Button asChild>
          <Link to="/routes/create">
            <PlusCircle className="mr-2 h-4 w-4" />
            创建路由规则
          </Link>
        </Button>
      </div>

      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索路由规则..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex h-[400px] items-center justify-center">
          <Loading text="加载中..." />
        </div>
      ) : filteredRoutes.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>名称</TableHead>
                <TableHead>命名空间</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRoutes.map((route) => (
                <TableRow key={route.id}>
                  <TableCell className="font-medium">{route.name}</TableCell>
                  <TableCell>{route.namespace}</TableCell>
                  <TableCell>{formatDateTime(route.createdAt)}</TableCell>
                  <TableCell>{formatDateTime(route.updatedAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="icon" asChild>
                        <Link to={`/routes/${route.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => setRouteToDelete(route)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认删除</AlertDialogTitle>
                            <AlertDialogDescription>
                              确定要删除路由规则 "{routeToDelete?.name}" 吗？此操作无法撤销。
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete}>删除</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="flex h-[400px] flex-col items-center justify-center rounded-md border">
          <p className="mb-4 text-muted-foreground">暂无路由规则</p>
          <Button asChild>
            <Link to="/routes/create">
              <PlusCircle className="mr-2 h-4 w-4" />
              创建路由规则
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
