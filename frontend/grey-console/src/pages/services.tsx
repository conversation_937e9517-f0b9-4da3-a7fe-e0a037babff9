import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getServices } from "@/lib/api";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search } from "lucide-react";
import { Loading } from "@/components/ui/loading";

export default function ServicesPage() {
  const [searchTerm, setSearchTerm] = useState("");

  // 获取服务
  const { data: services = {}, isLoading } = useQuery({
    queryKey: ["services"],
    queryFn: getServices,
  });

  // 过滤服务
  const filterServices = (namespaceServices: Record<string, string[]>) => {
    return Object.entries(namespaceServices).filter(([serviceName]) =>
      serviceName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  return (
    <div className="container">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">服务</h1>
        <p className="text-muted-foreground">查看各命名空间中的服务及其分支</p>
      </div>

      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索服务..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex h-[400px] items-center justify-center">
          <Loading text="加载中..." />
        </div>
      ) : Object.keys(services).length > 0 ? (
        <Tabs defaultValue={Object.keys(services)[0]} className="w-full">
          <TabsList className="mb-4">
            {Object.keys(services).map((namespace) => (
              <TabsTrigger key={namespace} value={namespace}>
                {namespace}
              </TabsTrigger>
            ))}
          </TabsList>
          {Object.entries(services).map(([namespace, namespaceServices]) => (
            <TabsContent key={namespace} value={namespace} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{namespace} 命名空间</CardTitle>
                  <CardDescription>该命名空间中的服务及其分支</CardDescription>
                </CardHeader>
                <CardContent>
                  {filterServices(namespaceServices).length > 0 ? (
                    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                      {filterServices(namespaceServices).map(([serviceName, branches]) => (
                        <div
                          key={serviceName}
                          className="rounded-lg border p-4 shadow-sm transition-all hover:shadow-md"
                        >
                          <h3 className="mb-2 text-lg font-semibold">{serviceName}</h3>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-muted-foreground">分支:</p>
                            <div className="flex flex-wrap gap-2">
                              {branches.map((branch) => (
                                <span
                                  key={branch}
                                  className="rounded-full bg-secondary px-2.5 py-0.5 text-xs font-medium"
                                >
                                  {branch}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground">没有找到匹配的服务</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <div className="flex h-[400px] flex-col items-center justify-center rounded-md border">
          <p className="text-muted-foreground">暂无服务数据</p>
        </div>
      )}
    </div>
  );
}
