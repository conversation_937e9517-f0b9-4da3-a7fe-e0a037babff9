import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

export default function TestPage() {
  const [count, setCount] = useState(0);

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-4 text-3xl font-bold">测试页面</h1>
      <p className="mb-4 text-lg">这是一个简单的测试页面，用于验证前端应用是否正常工作。</p>

      <div className="flex items-center gap-4">
        <Button onClick={() => setCount(count - 1)}>减少</Button>
        <span className="text-xl font-bold">{count}</span>
        <Button onClick={() => setCount(count + 1)}>增加</Button>
      </div>

      <div className="mt-8">
        <h2 className="mb-4 text-2xl font-bold">颜色测试</h2>
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="flex h-20 items-center justify-center rounded-md bg-primary text-primary-foreground">
            Primary
          </div>
          <div className="flex h-20 items-center justify-center rounded-md bg-secondary text-secondary-foreground">
            Secondary
          </div>
          <div className="flex h-20 items-center justify-center rounded-md bg-accent text-accent-foreground">
            Accent
          </div>
          <div className="flex h-20 items-center justify-center rounded-md bg-destructive text-destructive-foreground">
            Destructive
          </div>
        </div>
      </div>
    </div>
  );
}
