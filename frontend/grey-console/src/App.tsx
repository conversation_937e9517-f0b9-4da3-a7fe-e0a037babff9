import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import Dashboard from "@/pages/dashboard";
import RoutesPage from "@/pages/routes";
import RouteDetailPage from "@/pages/route-detail";
import CreateRoutePage from "@/pages/create-route";
import ServicesPage from "@/pages/services";
import NotFoundPage from "@/pages/not-found";
import TestPage from "@/pages/test";

function App() {
  return (
    <Router>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="flex min-h-screen flex-col font-sans antialiased">
          <Header />
          <main className="flex-1 p-4 md:p-6">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/routes" element={<RoutesPage />} />
              <Route path="/routes/create" element={<CreateRoutePage />} />
              <Route path="/routes/:id" element={<RouteDetailPage />} />
              <Route path="/services" element={<ServicesPage />} />
              <Route path="/test" element={<TestPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </main>
          <Footer />
          <Toaster />
        </div>
      </ThemeProvider>
    </Router>
  );
}

export default App;
