<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>基本HTML测试</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        color: #333;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #2563eb;
      }
      .btn {
        display: inline-block;
        background-color: #2563eb;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        margin-top: 20px;
      }
      .btn:hover {
        background-color: #1d4ed8;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>基本HTML测试页面</h1>
      <p>如果您能看到这个页面，说明服务器能够正确提供静态HTML文件。</p>
      <p>这个页面不依赖于React、TypeScript或任何其他前端框架，是一个纯HTML页面。</p>
      <p>如果这个页面可以正常显示，但React应用不能，那么问题可能出在React应用的配置或代码中。</p>
      <a href="/" class="btn">返回首页</a>
    </div>
  </body>
</html>
