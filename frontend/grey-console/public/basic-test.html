<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>基本功能测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        color: #333;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #2563eb;
      }
      button {
        background-color: #2563eb;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 8px;
      }
      button:hover {
        background-color: #1d4ed8;
      }
      .counter {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0;
      }
      .color-box {
        width: 100px;
        height: 100px;
        margin: 10px;
        display: inline-block;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>React 基本功能测试</h1>
      <p>这个页面使用CDN加载的React，用于测试基本的React功能是否正常工作。</p>

      <div id="counter-app"></div>

      <h2>颜色测试</h2>
      <div>
        <div class="color-box" style="background-color: #2563eb"></div>
        <div class="color-box" style="background-color: #10b981"></div>
        <div class="color-box" style="background-color: #ef4444"></div>
        <div class="color-box" style="background-color: #f59e0b"></div>
      </div>

      <p>如果您能看到上面的计数器正常工作，说明React基本功能正常。</p>
      <p>如果这个页面可以正常显示，但Vite应用不能，那么问题可能出在Vite配置或项目设置中。</p>

      <a href="/">返回首页</a>
    </div>

    <script type="text/babel">
      function Counter() {
        const [count, setCount] = React.useState(0);

        return (
          <div>
            <h2>计数器</h2>
            <div className="counter">{count}</div>
            <button onClick={() => setCount(count - 1)}>减少</button>
            <button onClick={() => setCount(count + 1)}>增加</button>
            <button onClick={() => setCount(0)}>重置</button>
          </div>
        );
      }

      ReactDOM.render(<Counter />, document.getElementById("counter-app"));
    </script>
  </body>
</html>
