#!/usr/bin/env bash

rootDir=$(git rev-parse --show-toplevel)
changedFiles=$(git diff --cached --name-only)

echo "Changed files:"
echo "$changedFiles"

# format backend/gateway/envoy-ext-proc
if echo "$changedFiles" | grep -q "^backend/gateway/envoy-ext-proc/"; then
    cd "${rootDir}/backend/gateway/envoy-ext-proc" && go fmt ./... && go mod tidy && golangci-lint run
fi

# format backend/istio-controller
if echo "$changedFiles" | grep -q "^backend/istio-controller/"; then
    cd "${rootDir}/backend/istio-controller" && go fmt ./... && go mod tidy && golangci-lint run
fi

# format backend/lib-java/core
if echo "$changedFiles" | grep -q "^backend/lib-java/core/"; then
    cd "${rootDir}/backend" && ./gradlew :lib-java:core:spotlessApply
fi

# format backend/lib-java/mybatis
if echo "$changedFiles" | grep -q "^backend/lib-java/mybatis/"; then
    cd "${rootDir}/backend" && ./gradlew :lib-java:mybatis:spotlessApply
fi

# format backend/proto
if echo "$changedFiles" | grep -q "^backend/proto/"; then
    cd "${rootDir}/backend" && buf format --write proto
fi

# format backend/service-app/service-app-server
if echo "$changedFiles" | grep -q "^backend/service-app/service-app-server/"; then
    cd "${rootDir}/backend" && ./gradlew :service-app:service-app-server:spotlessApply
fi

# format backend/service-crawler/service-crawler-api
if echo "$changedFiles" | grep -q "^backend/service-crawler/service-crawler-api/"; then
    cd "${rootDir}/backend" && buf format --write service-crawler/service-crawler-api/api
fi

# format backend/service-crawler/service-crawler-server
if echo "$changedFiles" | grep -q "^backend/service-crawler/service-crawler-server/"; then
    cd "${rootDir}/backend" && ./gradlew :service-crawler:service-crawler-server:spotlessApply
fi

# format backend/service-router/service-router-api
if echo "$changedFiles" | grep -q "^backend/service-router/service-router-api/"; then
    cd "${rootDir}/backend" && buf format --write service-router/service-router-api/api
fi

# format backend/service-router/service-router-server
if echo "$changedFiles" | grep -q "^backend/service-router/service-router-server/"; then
    cd "${rootDir}/backend" && ./gradlew :service-router:service-router-server:spotlessApply
fi

# format backend/service-task/service-task-server
if echo "$changedFiles" | grep -q "^backend/service-task/service-task-server/"; then
    cd "${rootDir}/backend" && ./gradlew :service-task:service-task-server:spotlessApply
fi

# format frontend/grey-console
if echo "$changedFiles" | grep -q "^frontend/grey-console/"; then
    cd "${rootDir}/frontend/grey-console" && pnpm format && pnpm lint
fi

# format frontend/web
if echo "$changedFiles" | grep -q "^frontend/web/"; then
    cd "${rootDir}/frontend/web" && pnpm format && pnpm lint
fi

# 添加所有修改
git add -u
